{"name": "vibe-my-home", "version": "1.0.0", "description": "AI-powered home space modification app", "private": true, "workspaces": ["client", "server", "shared"], "scripts": {"dev": "concurrently \"npm run dev:shared\" \"npm run dev:client\" \"npm run dev:server\"", "dev:client": "cd client && npm run dev", "dev:server": "cd server && npm run dev", "dev:shared": "cd shared && npm run dev", "build": "npm run build:shared && npm run build:client && npm run build:server", "build:client": "cd client && npm run build", "build:server": "cd server && npm run build", "build:shared": "cd shared && npm run build", "install:all": "npm install && cd client && npm install && cd ../server && npm install && cd ../shared && npm install"}, "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"node-fetch": "^3.3.2"}}