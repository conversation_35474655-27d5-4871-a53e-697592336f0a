{"name": "vibe-my-home-server", "version": "1.0.0", "description": "Server for Vibe My Home app", "main": "dist/server.js", "type": "module", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js"}, "dependencies": {"cors": "^2.8.5", "crypto-js": "^4.2.0", "express": "^4.18.2", "multer": "^1.4.5-lts.1", "uuid": "^9.0.0"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/crypto-js": "^4.2.2", "@types/express": "^4.17.17", "@types/multer": "^1.4.7", "@types/node": "^20.5.0", "@types/uuid": "^9.0.2", "tsx": "^3.12.7", "typescript": "^5.0.2"}}