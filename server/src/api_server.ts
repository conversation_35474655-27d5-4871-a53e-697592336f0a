import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';

export interface ImageSegment {
  id: string;
  category: string;
  coordinates: { x: number; y: number; width: number; height: number };
  confidence: number;
}

export interface CatalogItem {
  id: string;
  name: string;
  category: string;
  imageUrl: string;
  description: string;
}

export interface ProcessedImage {
  id: string;
  originalUrl: string;
  processedUrl?: string;
  segments: ImageSegment[];
  timestamp: Date;
}

// Mock data
const mockCatalogItems: CatalogItem[] = [
  {
    id: '1',
    name: 'Modern Sofa',
    category: 'sofas',
    imageUrl: 'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=300&h=200&fit=crop',
    description: 'Comfortable modern sofa'
  },
  {
    id: '2',
    name: 'Dining Table',
    category: 'tables',
    imageUrl: 'https://images.unsplash.com/photo-1449247709967-d4461a6a6103?w=300&h=200&fit=crop',
    description: 'Elegant wooden dining table'
  },
  {
    id: '3',
    name: 'Office Chair',
    category: 'chairs',
    imageUrl: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=200&fit=crop',
    description: 'Ergonomic office chair'
  },
  {
    id: '4',
    name: 'Coffee Table',
    category: 'tables',
    imageUrl: 'https://images.unsplash.com/photo-1506439773649-6e0eb8cfb237?w=300&h=200&fit=crop',
    description: 'Glass coffee table'
  },
  {
    id: '5',
    name: 'Armchair',
    category: 'chairs',
    imageUrl: 'https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=300&h=200&fit=crop',
    description: 'Comfortable armchair'
  },
  {
    id: '6',
    name: 'Sectional Sofa',
    category: 'sofas',
    imageUrl: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=200&fit=crop',
    description: 'Large sectional sofa'
  }
];

const mockPrompts = [
  'Make it more modern and minimalist',
  'Add warm, cozy lighting',
  'Change to a Scandinavian style',
  'Make it more colorful and vibrant',
  'Add plants and natural elements',
  'Transform to industrial style',
  'Make it more luxurious',
  'Add bohemian touches'
];

// In-memory storage for processed images
const processedImages: Map<string, ProcessedImage> = new Map();

// Helper function to calculate file checksum
function calculateChecksum(buffer: Buffer): string {
  return crypto.createHash('md5').update(buffer).digest('hex');
}

// Helper function to check if cached file exists
function getCachedImagePath(checksum: string): string | null {
  const imagePath = path.join('uploads', `image-${checksum}.jpeg`);
  if (fs.existsSync(imagePath)) {
    return imagePath;
  }
  return null;
}

// Helper function to get cached processed data
function getCachedProcessedData(checksum: string): ProcessedImage | null {
  const dataPath = path.join('uploads', `image-${checksum}.json`);
  if (fs.existsSync(dataPath)) {
    try {
      const data = fs.readFileSync(dataPath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      console.error('Error reading cached data:', error);
      return null;
    }
  }
  return null;
}

// Helper function to save processed data to cache
function saveCachedProcessedData(checksum: string, data: ProcessedImage): void {
  const dataPath = path.join('uploads', `image-${checksum}.json`);
  try {
    fs.writeFileSync(dataPath, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error saving cached data:', error);
  }
}

export function setupApiRoutes(app: express.Application, upload: any) {

  // Test endpoint
  app.get('/api/test', (req, res) => {
    res.json({ success: true, message: 'API is working!' });
  });

  // Upload and process image
  app.post('/api/upload', upload.single('image'), (req, res) => {
    try {
      console.log('Upload request received:', req.file ? 'File present' : 'No file');
      if (!req.file) {
        console.log('No file in request');
        return res.status(400).json({ error: 'No image file provided' });
      }

      console.log('File uploaded:', req.file.filename, 'Size:', req.file.size);

      // Calculate checksum of the uploaded file
      const fileBuffer = fs.readFileSync(req.file.path);
      const checksum = calculateChecksum(fileBuffer);
      console.log('File checksum:', checksum);

      // Check if we already have this image cached
      const cachedData = getCachedProcessedData(checksum);
      const cachedImagePath = getCachedImagePath(checksum);

      if (cachedData && cachedImagePath) {
        console.log('Using cached image and data for checksum:', checksum);

        // Remove the uploaded file since we have a cached version
        fs.unlinkSync(req.file.path);

        // Return cached data immediately
        const response = {
          success: true,
          imageId: cachedData.id,
          imageUrl: cachedData.originalUrl,
          segments: cachedData.segments,
          message: 'Image loaded from cache'
        };
        console.log('Sending cached response:', response);
        return res.json(response);
      }

      // New image - rename file to use checksum
      const newFilename = `image-${checksum}.jpeg`;
      const newFilePath = path.join('uploads', newFilename);

      // Move uploaded file to checksum-based name
      fs.renameSync(req.file.path, newFilePath);
      console.log('Renamed file to:', newFilename);

      const imageId = uuidv4();
      const imageUrl = `/uploads/${newFilename}`;

      // Mock segmentation - generate random segments
      const segments: ImageSegment[] = [
        {
          id: uuidv4(),
          category: 'sofas',
          coordinates: { x: 100, y: 150, width: 200, height: 120 },
          confidence: 0.95
        },
        {
          id: uuidv4(),
          category: 'tables',
          coordinates: { x: 350, y: 200, width: 150, height: 80 },
          confidence: 0.88
        },
        {
          id: uuidv4(),
          category: 'chairs',
          coordinates: { x: 50, y: 100, width: 80, height: 100 },
          confidence: 0.92
        }
      ];

      const processedImage: ProcessedImage = {
        id: imageId,
        originalUrl: imageUrl,
        segments,
        timestamp: new Date()
      };

      processedImages.set(imageId, processedImage);

      // Save processed data to cache
      saveCachedProcessedData(checksum, processedImage);

      // Simulate processing delay
      setTimeout(() => {
        const response = {
          success: true,
          imageId,
          imageUrl,
          segments,
          message: 'Image processed and segmented successfully'
        };
        console.log('Sending response:', response);
        res.json(response);
      }, 1000);

    } catch (error) {
      console.error('Upload error:', error);
      res.status(500).json({ error: 'Failed to process image' });
    }
  });

  // Get catalog items by category
  app.get('/api/catalog/:category?', (req, res) => {
    try {
      const { category } = req.params;
      
      let items = mockCatalogItems;
      if (category && category !== 'all') {
        items = mockCatalogItems.filter(item => item.category === category);
      }

      res.json({
        success: true,
        items,
        categories: ['all', 'sofas', 'chairs', 'tables', 'decor']
      });
    } catch (error) {
      console.error('Catalog error:', error);
      res.status(500).json({ error: 'Failed to fetch catalog items' });
    }
  });

  // Get canned prompts
  app.get('/api/prompts', (req, res) => {
    try {
      res.json({
        success: true,
        prompts: mockPrompts
      });
    } catch (error) {
      console.error('Prompts error:', error);
      res.status(500).json({ error: 'Failed to fetch prompts' });
    }
  });

  // Apply AI modification
  app.post('/api/modify', (req, res) => {
    try {
      const { imageId, prompt, segments } = req.body;

      if (!imageId || !prompt) {
        return res.status(400).json({ error: 'Image ID and prompt are required' });
      }

      const processedImage = processedImages.get(imageId);
      if (!processedImage) {
        return res.status(404).json({ error: 'Image not found' });
      }

      // Mock AI processing - return the same image with a "processed" flag
      const modifiedImageUrl = processedImage.originalUrl; // In real app, this would be a new processed image

      // Simulate processing delay
      setTimeout(() => {
        res.json({
          success: true,
          modifiedImageUrl,
          prompt,
          message: `Applied modification: "${prompt}"`
        });
      }, 2000);

    } catch (error) {
      console.error('Modify error:', error);
      res.status(500).json({ error: 'Failed to apply modification' });
    }
  });

  // Replace segment with catalog item
  app.post('/api/replace-segment', (req, res) => {
    try {
      const { imageId, segmentId, catalogItemId } = req.body;

      if (!imageId || !segmentId || !catalogItemId) {
        return res.status(400).json({ error: 'Image ID, segment ID, and catalog item ID are required' });
      }

      const processedImage = processedImages.get(imageId);
      if (!processedImage) {
        return res.status(404).json({ error: 'Image not found' });
      }

      const catalogItem = mockCatalogItems.find(item => item.id === catalogItemId);
      if (!catalogItem) {
        return res.status(404).json({ error: 'Catalog item not found' });
      }

      // Mock replacement - return success
      setTimeout(() => {
        res.json({
          success: true,
          message: `Replaced segment with ${catalogItem.name}`,
          modifiedImageUrl: processedImage.originalUrl // In real app, this would be a new processed image
        });
      }, 1500);

    } catch (error) {
      console.error('Replace segment error:', error);
      res.status(500).json({ error: 'Failed to replace segment' });
    }
  });

  // Upload supporting images
  app.post('/api/upload-supporting', upload.array('images', 5), (req, res) => {
    try {
      if (!req.files || req.files.length === 0) {
        return res.status(400).json({ error: 'No image files provided' });
      }

      const uploadedImages = (req.files as Express.Multer.File[]).map(file => ({
        id: uuidv4(),
        url: `/uploads/${file.filename}`,
        category: 'theme', // Mock categorization
        filename: file.originalname
      }));

      res.json({
        success: true,
        images: uploadedImages,
        message: 'Supporting images uploaded successfully'
      });

    } catch (error) {
      console.error('Supporting images upload error:', error);
      res.status(500).json({ error: 'Failed to upload supporting images' });
    }
  });
}
