import express from 'express';
import { SharedObjectService } from './sharedObjectService.js';

const router = express.Router();
const sharedObjectService = new SharedObjectService();

/**
 * GET /api/shared-objects - Get all objects
 */
router.get('/', (req, res) => {
  try {
    const objects = sharedObjectService.getAllObjects();
    res.json({
      success: true,
      data: objects,
      count: objects.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch objects',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/shared-objects/:id - Get a specific object
 */
router.get('/:id', (req, res) => {
  try {
    const obj = sharedObjectService.getObjectById(req.params.id);
    if (!obj) {
      return res.status(404).json({
        success: false,
        error: 'Object not found'
      });
    }
    res.json({
      success: true,
      data: obj
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch object',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/shared-objects - Create a new object
 */
router.post('/', (req, res) => {
  try {
    const { name, value } = req.body;
    
    if (!name) {
      return res.status(400).json({
        success: false,
        error: 'Name is required'
      });
    }

    const result = sharedObjectService.createObject(name, value || 0);
    
    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: result.errors
      });
    }

    res.status(201).json({
      success: true,
      data: result.object
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to create object',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * PUT /api/shared-objects/:id - Update an object
 */
router.put('/:id', (req, res) => {
  try {
    const { name, value } = req.body;
    const updates: any = {};
    
    if (name) updates.name = name;
    if (value !== undefined) updates.value = value;

    const result = sharedObjectService.updateObject(req.params.id, updates);
    
    if (!result.success) {
      const status = result.errors?.includes('Object not found') ? 404 : 400;
      return res.status(status).json({
        success: false,
        error: result.errors?.includes('Object not found') ? 'Object not found' : 'Validation failed',
        details: result.errors
      });
    }

    res.json({
      success: true,
      data: result.object
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to update object',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * DELETE /api/shared-objects/:id - Delete an object
 */
router.delete('/:id', (req, res) => {
  try {
    const result = sharedObjectService.deleteObject(req.params.id);
    
    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: 'Object not found'
      });
    }

    res.json({
      success: true,
      message: 'Object deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to delete object',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/shared-objects/:id/items - Add item to an object
 */
router.post('/:id/items', (req, res) => {
  try {
    const { item } = req.body;
    
    if (!item) {
      return res.status(400).json({
        success: false,
        error: 'Item is required'
      });
    }

    const result = sharedObjectService.addItemToObject(req.params.id, item);
    
    if (!result.success) {
      const status = result.errors?.includes('Object not found') ? 404 : 400;
      return res.status(status).json({
        success: false,
        error: result.errors?.includes('Object not found') ? 'Object not found' : 'Validation failed',
        details: result.errors
      });
    }

    res.status(201).json({
      success: true,
      data: result.object
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to add item',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * DELETE /api/shared-objects/:objectId/items/:item - Remove item from an object
 */
router.delete('/:objectId/items/:item', (req, res) => {
  try {
    const result = sharedObjectService.removeItemFromObject(
      req.params.objectId,
      decodeURIComponent(req.params.item)
    );
    
    if (!result.success) {
      const status = result.errors?.includes('Object not found') ? 404 : 400;
      return res.status(status).json({
        success: false,
        error: result.errors?.includes('Object not found') ? 'Object not found' : 'Item not found',
        details: result.errors
      });
    }

    res.json({
      success: true,
      data: result.object
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to remove item',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/shared-objects/stats/summary - Get statistics
 */
router.get('/stats/summary', (req, res) => {
  try {
    const stats = sharedObjectService.getStatistics();
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch statistics',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/shared-objects/search/query - Search objects
 */
router.get('/search/query', (req, res) => {
  try {
    const query = req.query.q as string;
    
    if (!query) {
      return res.status(400).json({
        success: false,
        error: 'Search query is required'
      });
    }

    const objects = sharedObjectService.searchObjects(query);
    res.json({
      success: true,
      data: objects,
      count: objects.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to search objects',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
