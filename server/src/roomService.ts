import { Room, FurnitureItem, RoomJSON, validateRoomDimensions, validateFurnitureItem, FURNITURE_CATEGORIES } from 'vibe-my-home-shared';
import { v4 as uuidv4 } from 'uuid';

/**
 * Server-side service for managing rooms using shared Room class
 */
export class RoomService {
  private rooms: Map<string, Room> = new Map();

  constructor() {
    // Initialize with some sample data
    this.initializeSampleData();
  }

  private initializeSampleData(): void {
    // Create a sample living room
    const livingRoom = new Room(
      uuidv4(),
      'Sample Living Room',
      { width: 6, height: 3, length: 8 }
    );

    // Add furniture to the living room
    const sofa = new FurnitureItem(
      uuidv4(),
      'L-Shaped So<PERSON>',
      FURNITURE_CATEGORIES.SEATING,
      { width: 2.5, height: 0.8, length: 1.5 },
      { x: 1, y: 0, z: 1 },
      '#8B4513',
      'fabric'
    );

    const tvStand = new FurnitureItem(
      uuidv4(),
      'TV Stand',
      FURNITURE_CATEGORIES.ELECTRONICS,
      { width: 1.5, height: 0.6, length: 0.4 },
      { x: 4, y: 0, z: 0.5 },
      '#654321',
      'wood'
    );

    const coffeeTable = new FurnitureItem(
      uuidv4(),
      'Glass Coffee Table',
      FURNITURE_CATEGORIES.TABLES,
      { width: 1.2, height: 0.4, length: 0.8 },
      { x: 2.5, y: 0, z: 2.5 },
      '#FFFFFF',
      'glass'
    );

    livingRoom.addFurniture(sofa);
    livingRoom.addFurniture(tvStand);
    livingRoom.addFurniture(coffeeTable);

    this.rooms.set(livingRoom.id, livingRoom);

    // Create a sample bedroom
    const bedroom = new Room(
      uuidv4(),
      'Master Bedroom',
      { width: 4, height: 3, length: 5 }
    );

    const bed = new FurnitureItem(
      uuidv4(),
      'King Size Bed',
      FURNITURE_CATEGORIES.BEDS,
      { width: 2, height: 0.6, length: 2.2 },
      { x: 1, y: 0, z: 1 },
      '#FFFFFF',
      'fabric'
    );

    const wardrobe = new FurnitureItem(
      uuidv4(),
      'Wardrobe',
      FURNITURE_CATEGORIES.STORAGE,
      { width: 1.5, height: 2.2, length: 0.6 },
      { x: 0.2, y: 0, z: 3 },
      '#8B4513',
      'wood'
    );

    bedroom.addFurniture(bed);
    bedroom.addFurniture(wardrobe);

    this.rooms.set(bedroom.id, bedroom);
  }

  /**
   * Get all rooms
   */
  public getAllRooms(): RoomJSON[] {
    return Array.from(this.rooms.values()).map(room => room.toJSON());
  }

  /**
   * Get a specific room by ID
   */
  public getRoomById(id: string): RoomJSON | null {
    const room = this.rooms.get(id);
    return room ? room.toJSON() : null;
  }

  /**
   * Create a new room
   */
  public createRoom(
    name: string,
    dimensions: { width: number; height: number; length: number }
  ): { success: boolean; room?: RoomJSON; errors?: string[] } {
    // Validate dimensions
    const validation = validateRoomDimensions(dimensions);
    if (!validation.isValid) {
      return { success: false, errors: validation.errors };
    }

    const newRoom = new Room(uuidv4(), name, dimensions);
    this.rooms.set(newRoom.id, newRoom);

    return { success: true, room: newRoom.toJSON() };
  }

  /**
   * Update a room
   */
  public updateRoom(
    id: string,
    updates: { name?: string; dimensions?: { width: number; height: number; length: number } }
  ): { success: boolean; room?: RoomJSON; errors?: string[] } {
    const room = this.rooms.get(id);
    if (!room) {
      return { success: false, errors: ['Room not found'] };
    }

    if (updates.dimensions) {
      const validation = validateRoomDimensions(updates.dimensions);
      if (!validation.isValid) {
        return { success: false, errors: validation.errors };
      }
      room.dimensions = updates.dimensions;
    }

    if (updates.name) {
      room.name = updates.name;
    }

    room.updatedAt = new Date();

    return { success: true, room: room.toJSON() };
  }

  /**
   * Delete a room
   */
  public deleteRoom(id: string): { success: boolean; errors?: string[] } {
    if (!this.rooms.has(id)) {
      return { success: false, errors: ['Room not found'] };
    }

    this.rooms.delete(id);
    return { success: true };
  }

  /**
   * Add furniture to a room
   */
  public addFurnitureToRoom(
    roomId: string,
    furnitureData: {
      name: string;
      category: string;
      dimensions: { width: number; height: number; length: number };
      position: { x: number; y: number; z: number };
      color?: string;
      material?: string;
    }
  ): { success: boolean; room?: RoomJSON; errors?: string[] } {
    const room = this.rooms.get(roomId);
    if (!room) {
      return { success: false, errors: ['Room not found'] };
    }

    // Validate furniture data
    const validation = validateFurnitureItem(furnitureData);
    if (!validation.isValid) {
      return { success: false, errors: validation.errors };
    }

    const furniture = new FurnitureItem(
      uuidv4(),
      furnitureData.name,
      furnitureData.category,
      furnitureData.dimensions,
      furnitureData.position,
      furnitureData.color,
      furnitureData.material
    );

    room.addFurniture(furniture);

    return { success: true, room: room.toJSON() };
  }

  /**
   * Remove furniture from a room
   */
  public removeFurnitureFromRoom(
    roomId: string,
    furnitureId: string
  ): { success: boolean; room?: RoomJSON; errors?: string[] } {
    const room = this.rooms.get(roomId);
    if (!room) {
      return { success: false, errors: ['Room not found'] };
    }

    const removed = room.removeFurniture(furnitureId);
    if (!removed) {
      return { success: false, errors: ['Furniture not found'] };
    }

    return { success: true, room: room.toJSON() };
  }

  /**
   * Get room statistics
   */
  public getRoomStatistics(): {
    totalRooms: number;
    totalFurnitureItems: number;
    averageRoomSize: number;
    averageOccupancy: number;
  } {
    const roomArray = Array.from(this.rooms.values());
    
    const totalRooms = roomArray.length;
    const totalFurnitureItems = roomArray.reduce((sum, room) => sum + room.furnitureItems.length, 0);
    const averageRoomSize = roomArray.reduce((sum, room) => sum + room.getFloorArea(), 0) / totalRooms;
    const averageOccupancy = roomArray.reduce((sum, room) => sum + (100 - room.getAvailableSpacePercentage()), 0) / totalRooms;

    return {
      totalRooms,
      totalFurnitureItems,
      averageRoomSize: Math.round(averageRoomSize * 100) / 100,
      averageOccupancy: Math.round(averageOccupancy * 100) / 100
    };
  }

  /**
   * Search rooms by name or furniture
   */
  public searchRooms(query: string): RoomJSON[] {
    const lowerQuery = query.toLowerCase();
    
    return Array.from(this.rooms.values())
      .filter(room => {
        // Search in room name
        if (room.name.toLowerCase().includes(lowerQuery)) {
          return true;
        }
        
        // Search in furniture names
        return room.furnitureItems.some(furniture => 
          furniture.name.toLowerCase().includes(lowerQuery) ||
          furniture.category.toLowerCase().includes(lowerQuery)
        );
      })
      .map(room => room.toJSON());
  }
}
