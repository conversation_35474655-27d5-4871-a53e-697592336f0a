import express from 'express';
import { RoomService } from './roomService.js';

const router = express.Router();
const roomService = new RoomService();

/**
 * GET /api/rooms - Get all rooms
 */
router.get('/', (req, res) => {
  try {
    const rooms = roomService.getAllRooms();
    res.json({
      success: true,
      data: rooms,
      count: rooms.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch rooms',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/rooms/:id - Get a specific room
 */
router.get('/:id', (req, res) => {
  try {
    const room = roomService.getRoomById(req.params.id);
    if (!room) {
      return res.status(404).json({
        success: false,
        error: 'Room not found'
      });
    }
    res.json({
      success: true,
      data: room
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch room',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/rooms - Create a new room
 */
router.post('/', (req, res) => {
  try {
    const { name, dimensions } = req.body;
    
    if (!name || !dimensions) {
      return res.status(400).json({
        success: false,
        error: 'Name and dimensions are required'
      });
    }

    const result = roomService.createRoom(name, dimensions);
    
    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: result.errors
      });
    }

    res.status(201).json({
      success: true,
      data: result.room
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to create room',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * PUT /api/rooms/:id - Update a room
 */
router.put('/:id', (req, res) => {
  try {
    const { name, dimensions } = req.body;
    const updates: any = {};
    
    if (name) updates.name = name;
    if (dimensions) updates.dimensions = dimensions;

    const result = roomService.updateRoom(req.params.id, updates);
    
    if (!result.success) {
      const status = result.errors?.includes('Room not found') ? 404 : 400;
      return res.status(status).json({
        success: false,
        error: result.errors?.includes('Room not found') ? 'Room not found' : 'Validation failed',
        details: result.errors
      });
    }

    res.json({
      success: true,
      data: result.room
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to update room',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * DELETE /api/rooms/:id - Delete a room
 */
router.delete('/:id', (req, res) => {
  try {
    const result = roomService.deleteRoom(req.params.id);
    
    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: 'Room not found'
      });
    }

    res.json({
      success: true,
      message: 'Room deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to delete room',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/rooms/:id/furniture - Add furniture to a room
 */
router.post('/:id/furniture', (req, res) => {
  try {
    const { name, category, dimensions, position, color, material } = req.body;
    
    if (!name || !category || !dimensions || !position) {
      return res.status(400).json({
        success: false,
        error: 'Name, category, dimensions, and position are required'
      });
    }

    const result = roomService.addFurnitureToRoom(req.params.id, {
      name,
      category,
      dimensions,
      position,
      color,
      material
    });
    
    if (!result.success) {
      const status = result.errors?.includes('Room not found') ? 404 : 400;
      return res.status(status).json({
        success: false,
        error: result.errors?.includes('Room not found') ? 'Room not found' : 'Validation failed',
        details: result.errors
      });
    }

    res.status(201).json({
      success: true,
      data: result.room
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to add furniture',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * DELETE /api/rooms/:roomId/furniture/:furnitureId - Remove furniture from a room
 */
router.delete('/:roomId/furniture/:furnitureId', (req, res) => {
  try {
    const result = roomService.removeFurnitureFromRoom(
      req.params.roomId,
      req.params.furnitureId
    );
    
    if (!result.success) {
      const status = result.errors?.includes('Room not found') ? 404 : 400;
      return res.status(status).json({
        success: false,
        error: result.errors?.includes('Room not found') ? 'Room not found' : 'Furniture not found',
        details: result.errors
      });
    }

    res.json({
      success: true,
      data: result.room
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to remove furniture',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/rooms/stats - Get room statistics
 */
router.get('/stats/summary', (req, res) => {
  try {
    const stats = roomService.getRoomStatistics();
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch statistics',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/rooms/search?q=query - Search rooms
 */
router.get('/search/query', (req, res) => {
  try {
    const query = req.query.q as string;
    
    if (!query) {
      return res.status(400).json({
        success: false,
        error: 'Search query is required'
      });
    }

    const rooms = roomService.searchRooms(query);
    res.json({
      success: true,
      data: rooms,
      count: rooms.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to search rooms',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
