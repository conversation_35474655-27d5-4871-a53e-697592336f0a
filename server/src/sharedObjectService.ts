import { SharedObjectExample, SharedObjectJSON, validateRoom<PERSON><PERSON> } from 'vibe-my-home-shared';
import { v4 as uuidv4 } from 'uuid';

/**
 * Server-side service for managing shared objects using SharedObjectExample class
 */
export class SharedObjectService {
  private objects: Map<string, SharedObjectExample> = new Map();

  constructor() {
    // Initialize with some sample data
    this.initializeSampleData();
  }

  private initializeSampleData(): void {
    // Create sample objects
    const obj1 = new SharedObjectExample(uuidv4(), 'Sample Object 1', 5);
    obj1.addItem('Apple');
    obj1.addItem('Banana');
    obj1.addItem('Cherry');

    const obj2 = new SharedObjectExample(uuidv4(), 'Sample Object 2', 3);
    obj2.addItem('Red');
    obj2.addItem('Green');

    this.objects.set(obj1.id, obj1);
    this.objects.set(obj2.id, obj2);
  }

  /**
   * Get all objects
   */
  public getAllObjects(): SharedObjectJSON[] {
    return Array.from(this.objects.values()).map(obj => obj.toJSON());
  }

  /**
   * Get a specific object by ID
   */
  public getObjectById(id: string): SharedObjectJSON | null {
    const obj = this.objects.get(id);
    return obj ? obj.toJSON() : null;
  }

  /**
   * Create a new object
   */
  public createObject(
    name: string,
    value: number
  ): { success: boolean; object?: SharedObjectJSON; errors?: string[] } {
    // Simple validation using shared function
    const validation = validateRoomName(name); // Reusing validation function
    if (!validation.isValid) {
      return { success: false, errors: validation.errors };
    }

    const newObject = new SharedObjectExample(uuidv4(), name, value);
    this.objects.set(newObject.id, newObject);

    return { success: true, object: newObject.toJSON() };
  }

  /**
   * Update an object
   */
  public updateObject(
    id: string,
    updates: { name?: string; value?: number }
  ): { success: boolean; object?: SharedObjectJSON; errors?: string[] } {
    const obj = this.objects.get(id);
    if (!obj) {
      return { success: false, errors: ['Object not found'] };
    }

    if (updates.name) {
      const validation = validateRoomName(updates.name);
      if (!validation.isValid) {
        return { success: false, errors: validation.errors };
      }
      obj.name = updates.name;
    }

    if (updates.value !== undefined) {
      obj.value = updates.value;
    }

    obj.updatedAt = new Date();

    return { success: true, object: obj.toJSON() };
  }

  /**
   * Delete an object
   */
  public deleteObject(id: string): { success: boolean; errors?: string[] } {
    if (!this.objects.has(id)) {
      return { success: false, errors: ['Object not found'] };
    }

    this.objects.delete(id);
    return { success: true };
  }

  /**
   * Add item to an object
   */
  public addItemToObject(
    objectId: string,
    item: string
  ): { success: boolean; object?: SharedObjectJSON; errors?: string[] } {
    const obj = this.objects.get(objectId);
    if (!obj) {
      return { success: false, errors: ['Object not found'] };
    }

    if (!item || item.trim().length === 0) {
      return { success: false, errors: ['Item name is required'] };
    }

    obj.addItem(item.trim());

    return { success: true, object: obj.toJSON() };
  }

  /**
   * Remove item from an object
   */
  public removeItemFromObject(
    objectId: string,
    item: string
  ): { success: boolean; object?: SharedObjectJSON; errors?: string[] } {
    const obj = this.objects.get(objectId);
    if (!obj) {
      return { success: false, errors: ['Object not found'] };
    }

    const removed = obj.removeItem(item);
    if (!removed) {
      return { success: false, errors: ['Item not found'] };
    }

    return { success: true, object: obj.toJSON() };
  }

  /**
   * Get statistics
   */
  public getStatistics(): {
    totalObjects: number;
    totalItems: number;
    averageValue: number;
    averageComputedValue: number;
  } {
    const objectArray = Array.from(this.objects.values());
    
    const totalObjects = objectArray.length;
    const totalItems = objectArray.reduce((sum, obj) => sum + obj.items.length, 0);
    const averageValue = objectArray.reduce((sum, obj) => sum + obj.value, 0) / totalObjects;
    const averageComputedValue = objectArray.reduce((sum, obj) => sum + obj.getComputedValue(), 0) / totalObjects;

    return {
      totalObjects,
      totalItems,
      averageValue: Math.round(averageValue * 100) / 100,
      averageComputedValue: Math.round(averageComputedValue * 100) / 100
    };
  }

  /**
   * Search objects by name or items
   */
  public searchObjects(query: string): SharedObjectJSON[] {
    const lowerQuery = query.toLowerCase();
    
    return Array.from(this.objects.values())
      .filter(obj => {
        // Search in object name
        if (obj.name.toLowerCase().includes(lowerQuery)) {
          return true;
        }
        
        // Search in items
        return obj.items.some(item => 
          item.toLowerCase().includes(lowerQuery)
        );
      })
      .map(obj => obj.toJSON());
  }
}
