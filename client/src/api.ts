import axios from 'axios';

const API_BASE_URL = '/api';

// Types
export interface ImageSegment {
  id: string;
  category: string;
  coordinates: { x: number; y: number; width: number; height: number };
  confidence: number;
}

export interface CatalogItem {
  id: string;
  name: string;
  category: string;
  imageUrl: string;
  description: string;
}

export interface UploadResponse {
  success: boolean;
  imageId: string;
  imageUrl: string;
  segments: ImageSegment[];
  message: string;
}

export interface CatalogResponse {
  success: boolean;
  items: CatalogItem[];
  categories: string[];
}

export interface PromptsResponse {
  success: boolean;
  prompts: string[];
}

export interface ModifyResponse {
  success: boolean;
  modifiedImageUrl: string;
  prompt: string;
  message: string;
}

export interface ReplaceSegmentResponse {
  success: boolean;
  message: string;
  modifiedImageUrl: string;
}

export interface SupportingImagesResponse {
  success: boolean;
  images: Array<{
    id: string;
    url: string;
    category: string;
    filename: string;
  }>;
  message: string;
}

// API Client class
class ApiClient {
  private axiosInstance;

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor for loading states
    this.axiosInstance.interceptors.request.use(
      (config) => {
        console.log(`Making ${config.method?.toUpperCase()} request to ${config.url}`);
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.axiosInstance.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        console.error('API Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  // Upload and process image
  async uploadImage(file: File): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append('image', file);

    console.log('Sending upload request...');

    return this.axiosInstance.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }).then(response => {
      console.log('Upload response received:', response);
      console.log('Response data:', response.data);
      console.log('Response status:', response.status);
      return response.data;
    }).catch(error => {
      console.error('Upload request failed:', error);
      console.error('Error response:', error.response);
      throw error;
    });
  }

  // Upload image from URL or base64
  async uploadImageFromData(imageData: string, filename: string): Promise<UploadResponse> {
    // Convert base64 to blob if needed
    let file: File;
    
    if (imageData.startsWith('data:')) {
      const response = await fetch(imageData);
      const blob = await response.blob();
      file = new File([blob], filename, { type: blob.type });
    } else {
      // Handle URL case - fetch the image
      const response = await fetch(imageData);
      const blob = await response.blob();
      file = new File([blob], filename, { type: blob.type });
    }

    return this.uploadImage(file);
  }

  // Get catalog items
  async getCatalogItems(category?: string): Promise<CatalogResponse> {
    const url = category ? `/catalog/${category}` : '/catalog';
    const response = await this.axiosInstance.get(url);
    return response.data;
  }

  // Get canned prompts
  async getPrompts(): Promise<PromptsResponse> {
    const response = await this.axiosInstance.get('/prompts');
    return response.data;
  }

  // Apply AI modification
  async modifyImage(imageId: string, prompt: string, segments?: ImageSegment[]): Promise<ModifyResponse> {
    const response = await this.axiosInstance.post('/modify', {
      imageId,
      prompt,
      segments,
    });
    return response.data;
  }

  // Replace segment with catalog item
  async replaceSegment(imageId: string, segmentId: string, catalogItemId: string): Promise<ReplaceSegmentResponse> {
    const response = await this.axiosInstance.post('/replace-segment', {
      imageId,
      segmentId,
      catalogItemId,
    });
    return response.data;
  }

  // Upload supporting images
  async uploadSupportingImages(files: File[]): Promise<SupportingImagesResponse> {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append('images', file);
    });

    const response = await this.axiosInstance.post('/upload-supporting', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  }

  // Health check
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    const response = await this.axiosInstance.get('/health');
    return response.data;
  }
}

// Create and export API client instance
export const api = new ApiClient();

// Utility functions
export const handleApiError = (error: any): string => {
  console.error('Full error object:', error);
  console.error('Error response:', error.response);
  console.error('Error message:', error.message);

  if (error.response?.data?.message) {
    console.error('Using response message:', error.response.data.message);
    return error.response.data.message;
  }
  if (error.response?.data?.error) {
    console.error('Using response error:', error.response.data.error);
    return error.response.data.error;
  }
  if (error.message) {
    console.error('Using error message:', error.message);
    return error.message;
  }
  console.error('Using fallback error message');
  return 'An unexpected error occurred';
};

export const isValidImageFile = (file: File): boolean => {
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  const maxSize = 10 * 1024 * 1024; // 10MB
  
  return validTypes.includes(file.type) && file.size <= maxSize;
};

export const convertFileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};
