import React, { useState, useEffect } from 'react';
import { useDrag } from 'react-dnd';
import { Search, Grid, List, Package } from 'lucide-react';
import { api, CatalogItem, handleApiError } from '../api';

interface CatalogSidebarProps {
  onError: (error: string) => void;
}

interface DraggableCatalogItemProps {
  item: CatalogItem;
  viewMode: 'grid' | 'list';
}

const DraggableCatalogItem: React.FC<DraggableCatalogItemProps> = ({ item, viewMode }) => {
  const [{ isDragging }, drag] = useDrag({
    type: 'catalog-item',
    item: { id: item.id, name: item.name, category: item.category },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  if (viewMode === 'list') {
    return (
      <div
        ref={drag}
        className={`flex items-center space-x-3 p-3 bg-gray-700 rounded-lg border border-gray-600 cursor-grab hover:shadow-md transition-all duration-200 ${
          isDragging ? 'opacity-50' : ''
        }`}
      >
        <img
          src={item.imageUrl}
          alt={item.name}
          className="w-12 h-12 object-cover rounded-lg"
        />
        <div className="flex-1 min-w-0">
          <h4 className="font-medium text-gray-100 truncate">{item.name}</h4>
          <p className="text-sm text-gray-400 truncate">{item.description}</p>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={drag}
      className={`catalog-item ${isDragging ? 'opacity-50' : ''}`}
    >
      <img
        src={item.imageUrl}
        alt={item.name}
        className="w-full h-32 object-cover"
      />
      <div className="p-3">
        <h4 className="font-medium text-gray-100 text-sm truncate">{item.name}</h4>
        <p className="text-xs text-gray-400 mt-1 truncate">{item.description}</p>
      </div>
    </div>
  );
};

const CatalogSidebar: React.FC<CatalogSidebarProps> = ({ onError }) => {
  const [items, setItems] = useState<CatalogItem[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCatalogItems();
  }, [selectedCategory]);

  const loadCatalogItems = async () => {
    try {
      setLoading(true);
      const response = await api.getCatalogItems(selectedCategory === 'all' ? undefined : selectedCategory);
      setItems(response.items);
      setCategories(response.categories);
    } catch (error) {
      onError(handleApiError(error));
    } finally {
      setLoading(false);
    }
  };

  const filteredItems = items.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'sofas':
        return '🛋️';
      case 'chairs':
        return '🪑';
      case 'tables':
        return '🪑';
      case 'decor':
        return '🏺';
      default:
        return '📦';
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Search */}
      <div className="p-4 border-b border-gray-700">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search catalog..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="input-field w-full pl-10 pr-4 py-2"
          />
        </div>
      </div>

      {/* Categories */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-medium text-gray-100">Categories</h3>
          <div className="flex items-center space-x-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-1 rounded ${viewMode === 'grid' ? 'bg-primary-900 bg-opacity-30 text-primary-400' : 'text-gray-400 hover:text-gray-300'}`}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-1 rounded ${viewMode === 'list' ? 'bg-primary-900 bg-opacity-30 text-primary-400' : 'text-gray-400 hover:text-gray-300'}`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-2">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`p-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                selectedCategory === category
                  ? 'bg-primary-900 bg-opacity-30 text-primary-400 border border-primary-600'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              <div className="flex items-center justify-center space-x-1">
                <span>{getCategoryIcon(category)}</span>
                <span className="capitalize">{category}</span>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Items */}
      <div className="flex-1 overflow-y-auto p-4">
        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="loading-spinner"></div>
          </div>
        ) : filteredItems.length === 0 ? (
          <div className="text-center py-8">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-400">
              {searchQuery ? 'No items match your search' : 'No items in this category'}
            </p>
          </div>
        ) : (
          <div className={viewMode === 'grid' ? 'grid grid-cols-2 gap-3' : 'space-y-3'}>
            {filteredItems.map((item) => (
              <DraggableCatalogItem
                key={item.id}
                item={item}
                viewMode={viewMode}
              />
            ))}
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="p-4 border-t border-gray-700 bg-gray-900 bg-opacity-30">
        <div className="text-xs text-gray-400">
          <p className="font-medium mb-1 text-gray-300">💡 How to use:</p>
          <p>Drag items from here onto segments in your image to replace furniture.</p>
        </div>
      </div>
    </div>
  );
};

export default CatalogSidebar;
