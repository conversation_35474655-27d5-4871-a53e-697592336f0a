import React, { useState } from 'react';
import { SharedObjectExample, validateRoomName } from 'vibe-my-home-shared';

/**
 * Simple example component demonstrating shared TypeScript classes
 */
export const SharedClassDemo: React.FC = () => {
  const [message, setMessage] = useState<string>('');
  const [validationResult, setValidationResult] = useState<string>('');

  // Simple demo function to show shared classes working
  const testSharedClasses = () => {
    // Create a shared object using shared class
    const sharedObj = new SharedObjectExample('demo-id', 'Demo Object', 10);

    // Add some items
    sharedObj.addItem('Item 1');
    sharedObj.addItem('Item 2');
    sharedObj.addItem('Item 3');

    // Use shared validation (keeping one validation example)
    const validation = validateRoomName('Test Name');

    // Display results
    setMessage(`Object created: ${sharedObj.getSummary()}`);
    setValidationResult(`Validation result: ${validation.isValid ? 'Valid' : 'Invalid'}`);
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Shared TypeScript Classes Demo</h1>

      <div className="mb-6 p-4 border rounded-lg">
        <h2 className="text-xl font-semibold mb-3">Test Shared Classes</h2>
        <p className="text-gray-600 mb-4">
          This demonstrates TypeScript classes shared between client and server.
        </p>

        <button
          onClick={testSharedClasses}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 mb-4"
        >
          Run Demo
        </button>

        {message && (
          <div className="p-3 bg-green-50 border border-green-200 rounded mb-2">
            <p className="text-green-800">{message}</p>
          </div>
        )}

        {validationResult && (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded">
            <p className="text-blue-800">{validationResult}</p>
          </div>
        )}
      </div>

      <div className="p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold mb-2">What this demonstrates:</h3>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• Room and FurnitureItem classes imported from shared package</li>
          <li>• Same business logic (calculations) on client and server</li>
          <li>• Shared validation functions</li>
          <li>• TypeScript types and constants</li>
        </ul>
      </div>
    </div>
  );
};
