import React, { useState, useEffect } from 'react';
import { Room, FurnitureItem, FURNITURE_CATEGORIES, validateRoomName, validateFurnitureItem } from 'vibe-my-home-shared';
import { v4 as uuidv4 } from 'uuid';

/**
 * Example React component using the shared Room class
 */
export const RoomManager: React.FC = () => {
  const [rooms, setRooms] = useState<Room[]>([]);
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [newRoomName, setNewRoomName] = useState('');
  const [newFurnitureName, setNewFurnitureName] = useState('');

  // Create a sample room on component mount
  useEffect(() => {
    const sampleRoom = new Room(
      uuidv4(),
      'Living Room',
      { width: 5, height: 3, length: 6 }
    );

    // Add some sample furniture
    const sofa = new FurnitureItem(
      uuidv4(),
      'Comfortable Sofa',
      FURNITURE_CATEGORIES.SEATING,
      { width: 2, height: 0.8, length: 0.9 },
      { x: 1, y: 0, z: 1 },
      '#8B4513',
      'fabric'
    );

    const coffeeTable = new FurnitureItem(
      uuidv4(),
      'Coffee Table',
      FURNITURE_CATEGORIES.TABLES,
      { width: 1.2, height: 0.4, length: 0.6 },
      { x: 2, y: 0, z: 2 },
      '#654321',
      'wood'
    );

    sampleRoom.addFurniture(sofa);
    sampleRoom.addFurniture(coffeeTable);

    setRooms([sampleRoom]);
    setSelectedRoom(sampleRoom);
  }, []);

  const createRoom = () => {
    const validation = validateRoomName(newRoomName);
    if (!validation.isValid) {
      alert(`Invalid room name: ${validation.errors.join(', ')}`);
      return;
    }

    const newRoom = new Room(
      uuidv4(),
      newRoomName,
      { width: 4, height: 3, length: 4 }
    );

    setRooms([...rooms, newRoom]);
    setNewRoomName('');
  };

  const addFurniture = () => {
    if (!selectedRoom) return;

    const newFurniture = new FurnitureItem(
      uuidv4(),
      newFurnitureName,
      FURNITURE_CATEGORIES.OTHER,
      { width: 1, height: 1, length: 1 },
      { x: 0, y: 0, z: 0 }
    );

    const validation = validateFurnitureItem(newFurniture);
    if (!validation.isValid) {
      alert(`Invalid furniture: ${validation.errors.join(', ')}`);
      return;
    }

    selectedRoom.addFurniture(newFurniture);
    setRooms([...rooms]); // Trigger re-render
    setNewFurnitureName('');
  };

  const removeFurniture = (furnitureId: string) => {
    if (!selectedRoom) return;
    
    selectedRoom.removeFurniture(furnitureId);
    setRooms([...rooms]); // Trigger re-render
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Room Manager</h1>
      
      {/* Room Creation */}
      <div className="mb-6 p-4 border rounded-lg">
        <h2 className="text-xl font-semibold mb-3">Create New Room</h2>
        <div className="flex gap-2">
          <input
            type="text"
            value={newRoomName}
            onChange={(e) => setNewRoomName(e.target.value)}
            placeholder="Room name"
            className="flex-1 px-3 py-2 border rounded"
          />
          <button
            onClick={createRoom}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Create Room
          </button>
        </div>
      </div>

      {/* Room List */}
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-3">Rooms</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {rooms.map((room) => (
            <div
              key={room.id}
              className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                selectedRoom?.id === room.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onClick={() => setSelectedRoom(room)}
            >
              <h3 className="font-semibold">{room.name}</h3>
              <p className="text-sm text-gray-600">
                {room.dimensions.width}m × {room.dimensions.length}m × {room.dimensions.height}m
              </p>
              <p className="text-sm text-gray-600">
                Floor Area: {room.getFloorArea()}m²
              </p>
              <p className="text-sm text-gray-600">
                Available Space: {room.getAvailableSpacePercentage().toFixed(1)}%
              </p>
              <p className="text-sm text-gray-600">
                Furniture Items: {room.furnitureItems.length}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Selected Room Details */}
      {selectedRoom && (
        <div className="p-4 border rounded-lg">
          <h2 className="text-xl font-semibold mb-3">
            {selectedRoom.name} Details
          </h2>
          
          {/* Room Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center p-2 bg-gray-100 rounded">
              <div className="font-semibold">{selectedRoom.getFloorArea()}m²</div>
              <div className="text-sm text-gray-600">Floor Area</div>
            </div>
            <div className="text-center p-2 bg-gray-100 rounded">
              <div className="font-semibold">{selectedRoom.getVolume()}m³</div>
              <div className="text-sm text-gray-600">Volume</div>
            </div>
            <div className="text-center p-2 bg-gray-100 rounded">
              <div className="font-semibold">{selectedRoom.getOccupiedFloorSpace().toFixed(1)}m²</div>
              <div className="text-sm text-gray-600">Occupied</div>
            </div>
            <div className="text-center p-2 bg-gray-100 rounded">
              <div className="font-semibold">{selectedRoom.getAvailableSpacePercentage().toFixed(1)}%</div>
              <div className="text-sm text-gray-600">Available</div>
            </div>
          </div>

          {/* Add Furniture */}
          <div className="mb-4">
            <h3 className="font-semibold mb-2">Add Furniture</h3>
            <div className="flex gap-2">
              <input
                type="text"
                value={newFurnitureName}
                onChange={(e) => setNewFurnitureName(e.target.value)}
                placeholder="Furniture name"
                className="flex-1 px-3 py-2 border rounded"
              />
              <button
                onClick={addFurniture}
                className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
              >
                Add Furniture
              </button>
            </div>
          </div>

          {/* Furniture List */}
          <div>
            <h3 className="font-semibold mb-2">Furniture ({selectedRoom.furnitureItems.length})</h3>
            <div className="space-y-2">
              {selectedRoom.furnitureItems.map((item) => (
                <div
                  key={item.id}
                  className="flex justify-between items-center p-3 bg-gray-50 rounded"
                >
                  <div>
                    <div className="font-medium">{item.name}</div>
                    <div className="text-sm text-gray-600">
                      {item.category} • {item.dimensions.width}×{item.dimensions.length}×{item.dimensions.height}m
                    </div>
                    <div className="text-sm text-gray-600">
                      Position: ({item.position.x}, {item.position.y}, {item.position.z})
                    </div>
                  </div>
                  <button
                    onClick={() => removeFurniture(item.id)}
                    className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600"
                  >
                    Remove
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
