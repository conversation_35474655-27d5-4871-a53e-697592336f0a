import React, { useState, useEffect } from 'react';
import { Wand2, Send, Sparkles, Upload } from 'lucide-react';
import { api, handleApiError } from '../api';

interface PromptInterfaceProps {
  imageId: string;
  onImageModified: (modifiedUrl: string) => void;
  onError: (error: string) => void;
  onProcessingChange: (processing: boolean) => void;
}

const PromptInterface: React.FC<PromptInterfaceProps> = ({
  imageId,
  onImageModified,
  onError,
  onProcessingChange
}) => {
  const [customPrompt, setCustomPrompt] = useState('');
  const [cannedPrompts, setCannedPrompts] = useState<string[]>([]);
  const [supportingImages, setSupportingImages] = useState<File[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadCannedPrompts();
  }, []);

  const loadCannedPrompts = async () => {
    try {
      const response = await api.getPrompts();
      setCannedPrompts(response.prompts);
    } catch (error) {
      onError(handleApiError(error));
    }
  };

  const handlePromptSubmit = async (prompt: string) => {
    if (!prompt.trim()) {
      onError('Please enter a prompt');
      return;
    }

    try {
      setLoading(true);
      onProcessingChange(true);
      
      const response = await api.modifyImage(imageId, prompt.trim());
      onImageModified(response.modifiedImageUrl);
      setCustomPrompt('');
    } catch (error) {
      onError(handleApiError(error));
    } finally {
      setLoading(false);
      onProcessingChange(false);
    }
  };

  const handleSupportingImagesUpload = async (files: FileList) => {
    const fileArray = Array.from(files);
    
    try {
      setLoading(true);
      onProcessingChange(true);
      
      await api.uploadSupportingImages(fileArray);
      setSupportingImages(prev => [...prev, ...fileArray]);
      
      // Auto-apply the theme from supporting images
      const themePrompt = `Apply the style and theme from the uploaded reference images`;
      await handlePromptSubmit(themePrompt);
      
    } catch (error) {
      onError(handleApiError(error));
    } finally {
      setLoading(false);
      onProcessingChange(false);
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* AI Modification Section */}
      <div className="card">
        <div className="flex items-center space-x-2 mb-4">
          <Wand2 className="w-5 h-5 text-primary-400" />
          <h3 className="text-lg font-semibold text-gray-100">AI Modifications</h3>
        </div>

        {/* Custom prompt */}
        <div className="space-y-3">
          <label className="block text-sm font-medium text-gray-300">
            Describe your vision
          </label>
          <div className="flex space-x-2">
            <textarea
              value={customPrompt}
              onChange={(e) => setCustomPrompt(e.target.value)}
              placeholder="e.g., Make it more modern with neutral colors and minimalist furniture..."
              className="input-field flex-1 min-h-[80px] resize-none"
              rows={3}
            />
            <button
              onClick={() => handlePromptSubmit(customPrompt)}
              disabled={!customPrompt.trim() || loading}
              className="btn-primary self-end disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Send className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Canned prompts */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-300 mb-3">
            Quick style options
          </label>
          <div className="grid grid-cols-1 gap-2">
            {cannedPrompts.map((prompt, index) => (
              <button
                key={index}
                onClick={() => handlePromptSubmit(prompt)}
                disabled={loading}
                className="text-left p-3 bg-gray-700 hover:bg-gray-600 rounded-lg border border-gray-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <div className="flex items-center space-x-2">
                  <Sparkles className="w-4 h-4 text-primary-400 flex-shrink-0" />
                  <span className="text-sm text-gray-300">{prompt}</span>
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Supporting Images Section */}
      <div className="card">
        <div className="flex items-center space-x-2 mb-4">
          <Upload className="w-5 h-5 text-secondary-400" />
          <h3 className="text-lg font-semibold text-gray-100">Reference Images</h3>
        </div>

        <div className="space-y-3">
          <p className="text-sm text-gray-400">
            Upload images to inspire the AI transformation
          </p>

          {/* File upload */}
          <div className="relative">
            <input
              type="file"
              multiple
              accept="image/*"
              onChange={(e) => e.target.files && handleSupportingImagesUpload(e.target.files)}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              disabled={loading}
            />
            <div className="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center hover:border-secondary-400 hover:bg-secondary-900 hover:bg-opacity-20 transition-all duration-200">
              <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-300">
                Click to upload reference images
              </p>
              <p className="text-xs text-gray-500 mt-1">
                PNG, JPG, GIF up to 10MB each
              </p>
            </div>
          </div>

          {/* Uploaded images preview */}
          {supportingImages.length > 0 && (
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-700">
                Uploaded references ({supportingImages.length})
              </p>
              <div className="grid grid-cols-2 gap-2">
                {supportingImages.slice(0, 4).map((file, index) => (
                  <div key={index} className="relative">
                    <img
                      src={URL.createObjectURL(file)}
                      alt={`Reference ${index + 1}`}
                      className="w-full h-20 object-cover rounded-lg"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-40 rounded-lg flex items-center justify-center">
                      <span className="text-white text-xs font-medium">
                        {file.name.length > 15 ? file.name.substring(0, 15) + '...' : file.name}
                      </span>
                    </div>
                  </div>
                ))}
                {supportingImages.length > 4 && (
                  <div className="bg-gray-100 rounded-lg h-20 flex items-center justify-center">
                    <span className="text-gray-600 text-sm">
                      +{supportingImages.length - 4} more
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Tips */}
      <div className="p-4 bg-gradient-to-r from-primary-50 to-secondary-50 rounded-lg border border-primary-200">
        <h4 className="font-medium text-gray-900 mb-2">💡 Pro Tips</h4>
        <ul className="text-sm text-gray-700 space-y-1">
          <li>• Be specific about colors, styles, and materials</li>
          <li>• Reference images help AI understand your vision better</li>
          <li>• Try different prompts to explore various design options</li>
          <li>• Combine catalog items with AI modifications for best results</li>
        </ul>
      </div>

      {/* Processing indicator */}
      {loading && (
        <div className="flex items-center justify-center p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="loading-spinner mr-3"></div>
          <span className="text-blue-700 font-medium">
            AI is working on your design...
          </span>
        </div>
      )}
    </div>
  );
};

export default PromptInterface;
