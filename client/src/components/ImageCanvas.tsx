import React, { useState, useCallback } from 'react';
import { useDrop } from 'react-dnd';
import { Trash2, RefreshCw } from 'lucide-react';
import { ImageSegment, CatalogItem, api, handleApiError } from '../api';

interface ImageCanvasProps {
  image: {
    id: string;
    url: string;
    segments: ImageSegment[];
  };
  onImageModified: (modifiedUrl: string) => void;
  onError: (error: string) => void;
  onProcessingChange: (processing: boolean) => void;
}

const ImageCanvas: React.FC<ImageCanvasProps> = ({
  image,
  onImageModified,
  onError,
  onProcessingChange
}) => {
  console.log('ImageCanvas received image:', image);
  console.log('Image URL:', image.url);

  const [selectedSegment, setSelectedSegment] = useState<string | null>(null);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [showReplacePopup, setShowReplacePopup] = useState(false);
  const [replacementItems, setReplacementItems] = useState<CatalogItem[]>([]);
  const [loadingReplacements, setLoadingReplacements] = useState(false);

  const [{ isOver }, drop] = useDrop({
    accept: 'catalog-item',
    drop: (item: { id: string; name: string; category: string }, monitor) => {
      if (!monitor.didDrop()) {
        handleCatalogItemDrop(item);
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  });

  const handleCatalogItemDrop = useCallback(async (catalogItem: { id: string; name: string; category: string }) => {
    if (!selectedSegment) {
      onError('Please select a segment first, then drag the item onto it');
      return;
    }

    try {
      onProcessingChange(true);
      const response = await api.replaceSegment(image.id, selectedSegment, catalogItem.id);
      onImageModified(response.modifiedImageUrl);
      setSelectedSegment(null);
    } catch (error) {
      onError(handleApiError(error));
    } finally {
      onProcessingChange(false);
    }
  }, [selectedSegment, image.id, onImageModified, onError, onProcessingChange]);

  const handleSegmentClick = useCallback((segmentId: string) => {
    setSelectedSegment(selectedSegment === segmentId ? null : segmentId);
  }, [selectedSegment]);

  const handleRemoveSegment = useCallback(async () => {
    if (!selectedSegment) return;

    try {
      onProcessingChange(true);
      // In a real app, you'd have an API endpoint to remove segments
      // For now, we'll just deselect the segment
      setSelectedSegment(null);
      onError('Segment removal feature coming soon!');
    } catch (error) {
      onError(handleApiError(error));
    } finally {
      onProcessingChange(false);
    }
  }, [selectedSegment, onProcessingChange, onError]);

  const handleOpenReplacePopup = useCallback(async (segmentId: string) => {
    const segment = image.segments.find(s => s.id === segmentId);
    if (!segment) return;

    try {
      setLoadingReplacements(true);
      const response = await api.getCatalogItems(segment.category);
      setReplacementItems(response.items);
      setShowReplacePopup(true);
    } catch (error) {
      onError(handleApiError(error));
    } finally {
      setLoadingReplacements(false);
    }
  }, [image.segments, onError]);

  const handleReplaceWithItem = useCallback(async (catalogItem: CatalogItem) => {
    if (!selectedSegment) return;

    try {
      onProcessingChange(true);
      const response = await api.replaceSegment(image.id, selectedSegment, catalogItem.id);
      onImageModified(response.modifiedImageUrl);
      setShowReplacePopup(false);
      setSelectedSegment(null);
    } catch (error) {
      onError(handleApiError(error));
    } finally {
      onProcessingChange(false);
    }
  }, [selectedSegment, image.id, onImageModified, onError, onProcessingChange]);

  const getSegmentStyle = (segment: ImageSegment) => ({
    left: `${segment.coordinates.x}px`,
    top: `${segment.coordinates.y}px`,
    width: `${segment.coordinates.width}px`,
    height: `${segment.coordinates.height}px`,
  });

  return (
    <div className="h-full flex flex-col">
      {/* Canvas area */}
      <div
        ref={drop}
        className={`flex-1 overflow-hidden bg-gray-900 relative ${isOver ? 'drop-target' : ''} flex items-center justify-center`}
      >
        <div className="relative max-w-full max-h-full">
          <img
            src={image.url}
            alt="Room to modify"
            className="block max-w-full max-h-full object-contain"
            onLoad={() => setImageLoaded(true)}
            onError={(e) => {
              console.error('Image load error:', e);
              console.error('Image URL:', image.url);
              console.error('Image src attribute:', e.currentTarget.src);
              console.error('Image naturalWidth:', e.currentTarget.naturalWidth);
              console.error('Image naturalHeight:', e.currentTarget.naturalHeight);
              onError(`Failed to load image: ${image.url}`);
            }}
          />

          {/* Segment overlays */}
          {imageLoaded && image.segments.map((segment) => (
            <div
              key={segment.id}
              className={`segment-overlay ${selectedSegment === segment.id ? 'selected' : ''} group`}
              style={getSegmentStyle(segment)}
              onClick={() => handleSegmentClick(segment.id)}
              title={`${segment.category} (${Math.round(segment.confidence * 100)}% confidence)`}
            >
              {/* Segment label */}
              <div className="absolute -top-6 left-0 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
                {segment.category}
              </div>

              {/* Action buttons - show when selected */}
              <div className="segment-actions absolute -top-6 right-0 flex items-center space-x-1 bg-white rounded shadow-lg border border-gray-200 p-1">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemoveSegment();
                  }}
                  className="p-1 text-red-600 hover:bg-red-50 rounded text-xs"
                  title="Remove segment"
                >
                  <Trash2 className="w-3 h-3" />
                </button>
                <div className="w-px h-4 bg-gray-200"></div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleOpenReplacePopup(segment.id);
                  }}
                  className="p-1 text-blue-600 hover:bg-blue-50 rounded text-xs"
                  title="Replace with item"
                  disabled={loadingReplacements}
                >
                  <RefreshCw className={`w-3 h-3 ${loadingReplacements ? 'animate-spin' : ''}`} />
                </button>
              </div>
            </div>
          ))}

          {/* Drop zone indicator */}
          {isOver && (
            <div className="absolute inset-0 bg-primary-500 bg-opacity-20 border-2 border-dashed border-primary-500 flex items-center justify-center">
              <div className="bg-white bg-opacity-90 px-4 py-2 rounded-lg text-primary-700 font-medium">
                {selectedSegment
                  ? `Drop to replace selected ${image.segments.find(s => s.id === selectedSegment)?.category || 'segment'}`
                  : 'Select a segment first by clicking on it'}
              </div>
            </div>
          )}
        </div>
      </div>



      {/* Replacement Popup */}
      {showReplacePopup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in">
          <div className="bg-gray-800 rounded-xl shadow-2xl max-w-2xl w-full mx-4 max-h-[80vh] flex flex-col animate-slide-up border border-gray-700">
            {/* Header */}
            <div className="p-6 border-b border-gray-700">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-100">
                  Replace {image.segments.find(s => s.id === selectedSegment)?.category || 'Item'}
                </h3>
                <button
                  onClick={() => setShowReplacePopup(false)}
                  className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
                >
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <p className="text-sm text-gray-400 mt-1">
                Choose a replacement from the {image.segments.find(s => s.id === selectedSegment)?.category} category
              </p>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto p-6">
              {loadingReplacements ? (
                <div className="flex items-center justify-center h-32">
                  <div className="loading-spinner"></div>
                  <span className="ml-3 text-gray-400">Loading items...</span>
                </div>
              ) : (
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {replacementItems.map((item) => (
                    <button
                      key={item.id}
                      onClick={() => handleReplaceWithItem(item)}
                      className="group bg-gray-700 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-200 transform hover:scale-105 cursor-pointer"
                    >
                      <img
                        src={item.imageUrl}
                        alt={item.name}
                        className="w-full h-32 object-cover"
                      />
                      <div className="p-3">
                        <h4 className="font-medium text-gray-100 text-sm truncate group-hover:text-primary-400">
                          {item.name}
                        </h4>
                        <p className="text-xs text-gray-400 mt-1 truncate">
                          {item.description}
                        </p>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="p-6 border-t border-gray-700 bg-gray-900 bg-opacity-30">
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowReplacePopup(false)}
                  className="btn-secondary"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageCanvas;
