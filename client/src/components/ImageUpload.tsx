import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, Image as ImageIcon, Clipboard, Link } from 'lucide-react';
import { api, isValidImageFile, handleApiError } from '../api';

interface ImageUploadProps {
  onImageProcessed: (imageData: { id: string; url: string; segments: any[] }) => void;
  onError: (error: string) => void;
  onProcessingChange: (processing: boolean) => void;
  onImagePreview: (previewUrl: string) => void;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  onImageProcessed,
  onError,
  onProcessingChange,
  onImagePreview
}) => {
  const [dragActive, setDragActive] = useState(false);
  const [urlInput, setUrlInput] = useState('');

  const processImage = useCallback(async (file: File) => {
    console.log('Processing image:', file.name, file.size, file.type);

    if (!isValidImageFile(file)) {
      onError('Please upload a valid image file (JPEG, PNG, GIF, WebP) under 10MB');
      return;
    }

    // Create preview URL immediately
    const previewUrl = URL.createObjectURL(file);
    onImagePreview(previewUrl);

    try {
      onProcessingChange(true);
      console.log('Calling API uploadImage...');
      const response = await api.uploadImage(file);
      console.log('API response:', response);

      if (!response || !response.success) {
        throw new Error(response?.message || 'Upload failed - invalid response');
      }

      console.log('Processing successful response...');
      // Clean up the preview URL since we now have the processed image
      URL.revokeObjectURL(previewUrl);

      onImageProcessed({
        id: response.imageId,
        url: response.imageUrl,
        segments: response.segments
      });
      console.log('onImageProcessed called successfully');
    } catch (error) {
      console.error('Upload error:', error);
      // Clean up preview URL on error
      URL.revokeObjectURL(previewUrl);
      onError(handleApiError(error));
    } finally {
      onProcessingChange(false);
    }
  }, [onImageProcessed, onError, onProcessingChange, onImagePreview]);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      processImage(acceptedFiles[0]);
    }
  }, [processImage]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp']
    },
    multiple: false,
    onDragEnter: () => setDragActive(true),
    onDragLeave: () => setDragActive(false),
  });

  const handlePaste = useCallback(async () => {
    try {
      const clipboardItems = await navigator.clipboard.read();
      
      for (const clipboardItem of clipboardItems) {
        for (const type of clipboardItem.types) {
          if (type.startsWith('image/')) {
            const blob = await clipboardItem.getType(type);
            const file = new File([blob], 'pasted-image.png', { type });
            processImage(file);
            return;
          }
        }
      }
      
      onError('No image found in clipboard');
    } catch (error) {
      onError('Failed to paste image from clipboard');
    }
  }, [processImage, onError]);

  const handleUrlUpload = useCallback(async () => {
    if (!urlInput.trim()) {
      onError('Please enter a valid image URL');
      return;
    }

    // Show preview immediately for URL
    onImagePreview(urlInput.trim());

    try {
      onProcessingChange(true);
      const response = await api.uploadImageFromData(urlInput.trim(), 'url-image.jpg');

      onImageProcessed({
        id: response.imageId,
        url: response.imageUrl,
        segments: response.segments
      });

      setUrlInput('');
    } catch (error) {
      onError(handleApiError(error));
    } finally {
      onProcessingChange(false);
    }
  }, [urlInput, onImageProcessed, onError, onProcessingChange, onImagePreview]);

  return (
    <div className="w-full max-w-2xl mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-100 mb-4">
          Transform Your Space
        </h2>
        <p className="text-lg text-gray-400">
          Upload a photo of your room and let AI help you redesign it
        </p>
      </div>

      {/* Main upload area */}
      <div
        {...getRootProps()}
        className={`upload-zone ${isDragActive || dragActive ? 'dragover' : ''} mb-6`}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 bg-primary-900 bg-opacity-30 rounded-full flex items-center justify-center mb-4">
            <Upload className="w-8 h-8 text-primary-400" />
          </div>

          <h3 className="text-xl font-semibold text-gray-100 mb-2">
            {isDragActive ? 'Drop your image here' : 'Upload your room photo'}
          </h3>

          <p className="text-gray-400 mb-4 text-center">
            Drag and drop an image, or click to browse
          </p>

          <div className="flex flex-wrap gap-2 text-sm text-gray-500">
            <span className="bg-gray-700 px-2 py-1 rounded">JPEG</span>
            <span className="bg-gray-700 px-2 py-1 rounded">PNG</span>
            <span className="bg-gray-700 px-2 py-1 rounded">GIF</span>
            <span className="bg-gray-700 px-2 py-1 rounded">WebP</span>
            <span className="bg-gray-700 px-2 py-1 rounded">Max 10MB</span>
          </div>
        </div>
      </div>

      {/* Alternative upload methods */}
      <div className="space-y-4">
        {/* URL upload */}
        <div className="card">
          <div className="flex items-center space-x-3 mb-3">
            <Link className="w-5 h-5 text-gray-600" />
            <h4 className="font-medium text-gray-900">Upload from URL</h4>
          </div>
          
          <div className="flex space-x-3">
            <input
              type="url"
              value={urlInput}
              onChange={(e) => setUrlInput(e.target.value)}
              placeholder="https://example.com/image.jpg"
              className="input-field flex-1"
              onKeyPress={(e) => e.key === 'Enter' && handleUrlUpload()}
            />
            <button
              onClick={handleUrlUpload}
              disabled={!urlInput.trim()}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Upload
            </button>
          </div>
        </div>

        {/* Paste from clipboard */}
        <div className="card">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Clipboard className="w-5 h-5 text-gray-600" />
              <div>
                <h4 className="font-medium text-gray-900">Paste from Clipboard</h4>
                <p className="text-sm text-gray-600">Copy an image and paste it here</p>
              </div>
            </div>
            
            <button
              onClick={handlePaste}
              className="btn-outline"
            >
              Paste Image
            </button>
          </div>
        </div>
      </div>

      {/* Tips */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <h4 className="font-medium text-blue-900 mb-2 flex items-center">
          <ImageIcon className="w-4 h-4 mr-2" />
          Tips for best results
        </h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Use well-lit photos with clear furniture visibility</li>
          <li>• Avoid cluttered or very dark images</li>
          <li>• Higher resolution images work better for segmentation</li>
          <li>• Make sure furniture items are clearly visible</li>
        </ul>
      </div>
    </div>
  );
};

export default ImageUpload;
