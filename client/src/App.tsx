import { useState, useCallback } from 'react';
import { Menu, HelpCircle, X } from 'lucide-react';
import ImageUpload from './components/ImageUpload';
import ImageCanvas from './components/ImageCanvas';
import CatalogSidebar from './components/CatalogSidebar';
import PromptInterface from './components/PromptInterface';
import Toast from './components/Toast';
import { ImageSegment } from './api';

interface ProcessedImage {
  id: string;
  url: string;
  segments: ImageSegment[];
}

function App() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [currentImage, setCurrentImage] = useState<ProcessedImage | null>(null);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' | 'warning' } | null>(null);

  const showToast = useCallback((message: string, type: 'success' | 'error' | 'warning' = 'success') => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 5000);
  }, []);

  const handleImagePreview = useCallback((previewUrl: string) => {
    setPreviewImage(previewUrl);
    setSidebarOpen(true); // Auto-open sidebar when image is uploaded
  }, []);

  const handleImageProcessed = useCallback((imageData: ProcessedImage) => {
    setCurrentImage(imageData);
    setPreviewImage(null); // Clear preview since we have the processed image
    showToast('Image processed successfully!');
  }, [showToast]);

  const handleImageModified = useCallback((modifiedUrl: string) => {
    if (currentImage) {
      setCurrentImage({
        ...currentImage,
        url: modifiedUrl
      });
      showToast('Image modified successfully!');
    }
  }, [currentImage, showToast]);

  const handleError = useCallback((error: string) => {
    showToast(error, 'error');
  }, [showToast]);

  return (
    <div className="min-h-screen bg-gray-900 flex">
      {/* Main content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="bg-gray-800 border-b border-gray-700 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-gradient">Vibe My Space</h1>
              <p className="text-sm text-gray-400">Transform your space with AI</p>
            </div>

            <div className="flex items-center space-x-4">
              {isProcessing && (
                <div className="flex items-center space-x-2 text-sm text-primary-400">
                  <div className="loading-spinner w-4 h-4"></div>
                  <span>Processing...</span>
                </div>
              )}

              <button
                onClick={() => setShowHelp(true)}
                className="p-2 hover:bg-gray-700 rounded-lg text-gray-300"
                title="Help & Tips"
              >
                <HelpCircle className="w-5 h-5" />
              </button>

              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="p-2 hover:bg-gray-700 rounded-lg text-gray-300"
                title="Toggle Catalog"
              >
                <Menu className="w-5 h-5" />
              </button>
            </div>
          </div>
        </header>

        {/* Main workspace */}
        <main className="flex-1 flex">
          {/* Image workspace */}
          <div className="flex-1 flex flex-col">
            {!currentImage && !previewImage ? (
              <div className="flex-1 flex items-center justify-center p-6">
                <ImageUpload
                  onImageProcessed={handleImageProcessed}
                  onImagePreview={handleImagePreview}
                  onError={handleError}
                  onProcessingChange={setIsProcessing}
                />
              </div>
            ) : (
              <div className="flex-1 flex flex-col">
                {/* Image Canvas - 75% height */}
                <div className="h-[75vh] m-6 mb-3 bg-gray-800 rounded-xl shadow-sm border border-gray-700 overflow-hidden relative">
                  {currentImage ? (
                    <ImageCanvas
                      image={currentImage}
                      onImageModified={handleImageModified}
                      onError={handleError}
                      onProcessingChange={setIsProcessing}
                    />
                  ) : previewImage ? (
                    <>
                      {/* Preview Image */}
                      <div className="h-full flex items-center justify-center bg-gray-900">
                        <img
                          src={previewImage}
                          alt="Preview"
                          className="max-w-full max-h-full object-contain"
                        />
                      </div>

                      {/* Processing Overlay */}
                      {isProcessing && (
                        <div className="absolute inset-0 bg-black bg-opacity-60 flex items-center justify-center">
                          <div className="bg-gray-800 rounded-xl p-8 border border-gray-700 text-center">
                            <div className="relative mb-4">
                              <div className="loading-spinner w-12 h-12 mx-auto"></div>
                              <div className="absolute inset-0 rounded-full border-2 border-primary-500 border-opacity-20 animate-pulse"></div>
                            </div>
                            <h3 className="text-lg font-semibold text-gray-100 mb-2">Processing Image</h3>
                            <p className="text-gray-400 text-sm">
                              AI is analyzing your image and detecting furniture segments...
                            </p>
                            <div className="mt-4 flex justify-center space-x-1">
                              <div className="w-2 h-2 bg-primary-500 rounded-full animate-bounce"></div>
                              <div className="w-2 h-2 bg-primary-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                              <div className="w-2 h-2 bg-primary-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                            </div>
                          </div>
                        </div>
                      )}
                    </>
                  ) : null}
                </div>

                {/* Prompt interface at bottom - remaining space */}
                {currentImage && (
                  <div className="flex-1 px-6 pb-6">
                    <PromptInterface
                      imageId={currentImage.id}
                      onImageModified={handleImageModified}
                      onError={handleError}
                      onProcessingChange={setIsProcessing}
                    />
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Right Sidebar - Catalog */}
          <div className={`transition-all duration-300 ease-in-out ${sidebarOpen ? 'w-80' : 'w-0'} overflow-hidden`}>
            <div className="w-80 h-full bg-gray-800 border-l border-gray-700">
              <CatalogSidebar onError={handleError} />
            </div>
          </div>
        </main>
      </div>

      {/* Help Popup */}
      {showHelp && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in">
          <div className="bg-gray-800 rounded-xl shadow-2xl max-w-2xl w-full mx-4 max-h-[80vh] flex flex-col animate-slide-up border border-gray-700">
            {/* Header */}
            <div className="p-6 border-b border-gray-700">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-100">
                  How to Use Vibe My Space
                </h3>
                <button
                  onClick={() => setShowHelp(false)}
                  className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5 text-gray-400" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto p-6">
              <div className="space-y-6">
                <div>
                  <h4 className="font-medium text-gray-100 mb-3 flex items-center">
                    🖼️ Getting Started
                  </h4>
                  <ul className="space-y-2 text-sm text-gray-300">
                    <li>• Upload a photo of your room using drag & drop or file picker</li>
                    <li>• AI will automatically detect furniture segments</li>
                    <li>• Catalog sidebar opens automatically after upload</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium text-gray-100 mb-3 flex items-center">
                    🎯 Selecting & Replacing Items
                  </h4>
                  <ul className="space-y-2 text-sm text-gray-300">
                    <li>• Click on highlighted segments to select them</li>
                    <li>• Use the delete (🗑️) button to remove segments</li>
                    <li>• Use the replace (🔄) button to choose from category items</li>
                    <li>• Or drag catalog items directly onto selected segments</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium text-gray-100 mb-3 flex items-center">
                    🤖 AI Modifications
                  </h4>
                  <ul className="space-y-2 text-sm text-gray-300">
                    <li>• Use custom prompts to describe your vision</li>
                    <li>• Try quick style options for instant transformations</li>
                    <li>• Upload reference images to inspire the AI</li>
                    <li>• Be specific about colors, styles, and materials</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium text-gray-100 mb-3 flex items-center">
                    📚 Using the Catalog
                  </h4>
                  <ul className="space-y-2 text-sm text-gray-300">
                    <li>• Browse items by category (sofas, chairs, tables, decor)</li>
                    <li>• Switch between grid and list view</li>
                    <li>• Search for specific items</li>
                    <li>• Drag items onto segments or use replace popup</li>
                  </ul>
                </div>

                <div className="p-4 bg-primary-900 bg-opacity-20 rounded-lg border border-primary-600">
                  <h4 className="font-medium text-primary-400 mb-2">💡 Pro Tips</h4>
                  <ul className="space-y-1 text-sm text-gray-300">
                    <li>• Use well-lit photos for better segment detection</li>
                    <li>• Reference images help AI understand your style better</li>
                    <li>• Combine catalog items with AI modifications for best results</li>
                    <li>• Try different prompts to explore various design options</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Toast notifications */}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  );
}

export default App;
