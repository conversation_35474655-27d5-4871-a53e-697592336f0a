@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply bg-gray-900 text-gray-100;
    background-image:
      radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(147, 51, 234, 0.1) 0%, transparent 50%),
      linear-gradient(135deg, rgba(17, 24, 39, 0.8) 0%, rgba(31, 41, 55, 0.8) 100%);
    background-attachment: fixed;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-900;
  }

  .btn-secondary {
    @apply bg-gray-700 hover:bg-gray-600 text-gray-200 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-gray-900;
  }

  .btn-outline {
    @apply border-2 border-primary-600 text-primary-400 hover:bg-primary-600 hover:text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-900;
  }

  .card {
    @apply bg-gray-800 rounded-xl shadow-sm border border-gray-700 p-6;
  }

  .input-field {
    @apply w-full px-3 py-2 bg-gray-700 border border-gray-600 text-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 placeholder-gray-400;
  }
  
  .upload-zone {
    @apply border-2 border-dashed border-gray-600 rounded-xl p-8 text-center hover:border-primary-400 hover:bg-primary-900 hover:bg-opacity-20 transition-all duration-200 cursor-pointer bg-gray-800 bg-opacity-50;
  }

  .upload-zone.dragover {
    @apply border-primary-500 bg-primary-900 bg-opacity-30;
  }
  
  .segment-overlay {
    @apply absolute border-2 border-primary-500 bg-primary-500 bg-opacity-20 cursor-pointer transition-all duration-200 hover:bg-opacity-30;
  }

  .segment-overlay.selected {
    @apply border-secondary-500 bg-secondary-500 bg-opacity-30 shadow-lg;
  }

  .segment-overlay:hover .segment-actions {
    @apply opacity-100;
  }

  .segment-actions {
    @apply opacity-0 transition-opacity duration-200;
  }

  .segment-overlay.selected .segment-actions {
    @apply opacity-100;
  }
  
  .catalog-item {
    @apply bg-gray-800 rounded-lg shadow-sm border border-gray-700 overflow-hidden cursor-grab hover:shadow-md transition-all duration-200 transform hover:scale-105;
  }

  .catalog-item:active {
    @apply cursor-grabbing scale-95;
  }

  .catalog-item:hover {
    cursor: grab;
  }

  .catalog-item:active {
    cursor: grabbing;
  }

  .loading-spinner {
    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-primary-400;
  }

  .toast {
    @apply fixed top-4 right-4 bg-gray-800 border border-gray-700 rounded-lg shadow-lg p-4 max-w-sm z-50 animate-slide-up;
  }

  .toast.success {
    @apply border-green-600 bg-green-900 bg-opacity-20;
  }

  .toast.error {
    @apply border-red-600 bg-red-900 bg-opacity-20;
  }

  .toast.warning {
    @apply border-yellow-600 bg-yellow-900 bg-opacity-20;
  }

  .sidebar {
    @apply bg-gray-800 border-r border-gray-700 h-full overflow-y-auto;
  }

  .main-content {
    @apply flex-1 p-6 overflow-y-auto;
  }
}

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .glass {
    @apply bg-white bg-opacity-80 backdrop-blur-sm;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }
}

/* Custom animations */
@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Drag and drop styles */
.dragging {
  @apply opacity-50 transform rotate-3;
}

.drop-target {
  @apply ring-2 ring-primary-500 ring-opacity-50;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .sidebar {
    @apply fixed inset-y-0 left-0 z-50 w-64 transform -translate-x-full transition-transform duration-300 ease-in-out;
  }
  
  .sidebar.open {
    @apply translate-x-0;
  }
  
  .main-content {
    @apply p-4;
  }
}
