# Code Tech Stack

- Vite
- React
- Tailwind
- Typescript
- Both client and server merged (but server in the server folder, and client in the client folder)
- Themes setup by default.
- Server also Typescript
- it is a monorepo.
- responsive by default.
- smooth transitions where apt.
- good user feedback for actions.

## Client important files

- api.ts -> all the api calls are made from here.

## Server important files

- server.ts -> the server entry point.
- api_server.ts -> all the api calls implemented here. for now returns all dummy values