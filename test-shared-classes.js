#!/usr/bin/env node

/**
 * Test script to demonstrate shared TypeScript classes working between client and server
 */

import fetch from 'node-fetch';

const SERVER_URL = 'http://localhost:3001';

async function testSharedClasses() {
  console.log('🧪 Testing shared TypeScript classes between client and server...\n');

  try {
    // Test 1: Get all shared objects (should return sample data created by SharedObjectService)
    console.log('1️⃣ Testing GET /api/shared-objects - Fetch all objects');
    const objectsResponse = await fetch(`${SERVER_URL}/api/shared-objects`);
    const objectsData = await objectsResponse.json();

    if (objectsData.success) {
      console.log(`✅ Found ${objectsData.count} objects:`);
      objectsData.data.forEach(obj => {
        console.log(`   - ${obj.name}: ${obj.items.length} items, computed value: ${obj.computedValue}`);
      });
    } else {
      console.log('❌ Failed to fetch objects');
      return;
    }

    // Test 2: Create a new object using shared validation
    console.log('\n2️⃣ Testing POST /api/shared-objects - Create new object');
    const newObjectData = {
      name: 'Test Object',
      value: 7
    };

    const createResponse = await fetch(`${SERVER_URL}/api/shared-objects`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(newObjectData)
    });

    const createData = await createResponse.json();
    if (createData.success) {
      console.log(`✅ Created object: ${createData.data.name} (ID: ${createData.data.id})`);
      console.log(`   Value: ${createData.data.value}`);
      console.log(`   Summary: ${createData.data.summary}`);
    } else {
      console.log('❌ Failed to create object:', createData.details);
      return;
    }

    const newObjectId = createData.data.id;

    // Test 3: Add items to the object
    console.log('\n3️⃣ Testing POST /api/shared-objects/:id/items - Add items');
    const itemData = { item: 'New Item' };

    const itemResponse = await fetch(`${SERVER_URL}/api/shared-objects/${newObjectId}/items`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(itemData)
    });

    const itemResponseData = await itemResponse.json();
    if (itemResponseData.success) {
      console.log(`✅ Added item: ${itemData.item}`);
      console.log(`   Items count: ${itemResponseData.data.items.length}`);
      console.log(`   Computed value: ${itemResponseData.data.computedValue}`);
    } else {
      console.log('❌ Failed to add item:', itemResponseData.details);
    }

    // Test 4: Test validation with invalid data
    console.log('\n4️⃣ Testing validation - Try to create invalid object');
    const invalidObjectData = {
      name: '',
      value: 5
    };

    const invalidResponse = await fetch(`${SERVER_URL}/api/shared-objects`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(invalidObjectData)
    });

    const invalidData = await invalidResponse.json();
    if (!invalidData.success) {
      console.log('✅ Validation correctly rejected invalid object:');
      if (invalidData.details && Array.isArray(invalidData.details)) {
        invalidData.details.forEach(error => console.log(`   - ${error}`));
      } else {
        console.log(`   - ${invalidData.error || 'Validation failed'}`);
      }
    } else {
      console.log('❌ Validation should have failed but didn\'t');
    }

    // Test 5: Get object statistics
    console.log('\n5️⃣ Testing GET /api/shared-objects/stats/summary - Object statistics');
    const statsResponse = await fetch(`${SERVER_URL}/api/shared-objects/stats/summary`);
    const statsData = await statsResponse.json();

    if (statsData.success) {
      console.log('✅ Object statistics:');
      console.log(`   Total objects: ${statsData.data.totalObjects}`);
      console.log(`   Total items: ${statsData.data.totalItems}`);
      console.log(`   Average value: ${statsData.data.averageValue}`);
      console.log(`   Average computed value: ${statsData.data.averageComputedValue}`);
    } else {
      console.log('❌ Failed to get statistics');
    }

    // Test 6: Search objects
    console.log('\n6️⃣ Testing GET /api/shared-objects/search/query - Search functionality');
    const searchResponse = await fetch(`${SERVER_URL}/api/shared-objects/search/query?q=sample`);
    const searchData = await searchResponse.json();

    if (searchData.success) {
      console.log(`✅ Search found ${searchData.count} objects matching "sample":`);
      searchData.data.forEach(obj => {
        console.log(`   - ${obj.name}`);
      });
    } else {
      console.log('❌ Search failed');
    }

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ Shared TypeScript classes work between client and server');
    console.log('✅ SharedObjectExample class can be imported and used');
    console.log('✅ Validation functions work consistently');
    console.log('✅ JSON serialization/deserialization works');
    console.log('✅ Server can perform operations using shared logic');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\n💡 Make sure the server is running on http://localhost:3001');
    console.log('   Run: npm run dev:server');
  }
}

// Check if server is running first
async function checkServer() {
  try {
    const response = await fetch(`${SERVER_URL}/health`);
    if (response.ok) {
      return true;
    }
  } catch (error) {
    return false;
  }
  return false;
}

async function main() {
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    console.log('❌ Server is not running on http://localhost:3001');
    console.log('💡 Please start the server first:');
    console.log('   cd server && npm run dev');
    console.log('   or from root: npm run dev:server');
    process.exit(1);
  }

  await testSharedClasses();
}

main().catch(console.error);
