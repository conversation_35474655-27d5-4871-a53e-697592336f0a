#!/usr/bin/env node

/**
 * Test script to demonstrate shared TypeScript classes working between client and server
 */

import fetch from 'node-fetch';

const SERVER_URL = 'http://localhost:3001';

async function testSharedClasses() {
  console.log('🧪 Testing shared TypeScript classes between client and server...\n');

  try {
    // Test 1: Get all rooms (should return sample data created by RoomService)
    console.log('1️⃣ Testing GET /api/rooms - Fetch all rooms');
    const roomsResponse = await fetch(`${SERVER_URL}/api/rooms`);
    const roomsData = await roomsResponse.json();
    
    if (roomsData.success) {
      console.log(`✅ Found ${roomsData.count} rooms:`);
      roomsData.data.forEach(room => {
        console.log(`   - ${room.name}: ${room.floorArea}m² floor area, ${room.furnitureItems.length} furniture items`);
      });
    } else {
      console.log('❌ Failed to fetch rooms');
      return;
    }

    // Test 2: Create a new room using shared validation
    console.log('\n2️⃣ Testing POST /api/rooms - Create new room');
    const newRoomData = {
      name: 'Test Kitchen',
      dimensions: { width: 4, height: 3, length: 5 }
    };

    const createResponse = await fetch(`${SERVER_URL}/api/rooms`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(newRoomData)
    });

    const createData = await createResponse.json();
    if (createData.success) {
      console.log(`✅ Created room: ${createData.data.name} (ID: ${createData.data.id})`);
      console.log(`   Floor area: ${createData.data.floorArea}m²`);
      console.log(`   Available space: ${createData.data.availableSpacePercentage}%`);
    } else {
      console.log('❌ Failed to create room:', createData.details);
      return;
    }

    const newRoomId = createData.data.id;

    // Test 3: Add furniture to the room
    console.log('\n3️⃣ Testing POST /api/rooms/:id/furniture - Add furniture');
    const furnitureData = {
      name: 'Kitchen Island',
      category: 'tables',
      dimensions: { width: 2, height: 0.9, length: 1 },
      position: { x: 1, y: 0, z: 2 },
      color: '#FFFFFF',
      material: 'wood'
    };

    const furnitureResponse = await fetch(`${SERVER_URL}/api/rooms/${newRoomId}/furniture`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(furnitureData)
    });

    const furnitureResponseData = await furnitureResponse.json();
    if (furnitureResponseData.success) {
      console.log(`✅ Added furniture: ${furnitureData.name}`);
      console.log(`   Occupied space: ${furnitureResponseData.data.occupiedFloorSpace}m²`);
      console.log(`   Available space: ${furnitureResponseData.data.availableSpacePercentage.toFixed(1)}%`);
    } else {
      console.log('❌ Failed to add furniture:', furnitureResponseData.details);
    }

    // Test 4: Test validation with invalid data
    console.log('\n4️⃣ Testing validation - Try to create invalid room');
    const invalidRoomData = {
      name: '',
      dimensions: { width: -1, height: 0, length: 100 }
    };

    const invalidResponse = await fetch(`${SERVER_URL}/api/rooms`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(invalidRoomData)
    });

    const invalidData = await invalidResponse.json();
    if (!invalidData.success) {
      console.log('✅ Validation correctly rejected invalid room:');
      if (invalidData.details && Array.isArray(invalidData.details)) {
        invalidData.details.forEach(error => console.log(`   - ${error}`));
      } else {
        console.log(`   - ${invalidData.error || 'Validation failed'}`);
      }
    } else {
      console.log('❌ Validation should have failed but didn\'t');
    }

    // Test 5: Get room statistics
    console.log('\n5️⃣ Testing GET /api/rooms/stats/summary - Room statistics');
    const statsResponse = await fetch(`${SERVER_URL}/api/rooms/stats/summary`);
    const statsData = await statsResponse.json();
    
    if (statsData.success) {
      console.log('✅ Room statistics:');
      console.log(`   Total rooms: ${statsData.data.totalRooms}`);
      console.log(`   Total furniture: ${statsData.data.totalFurnitureItems}`);
      console.log(`   Average room size: ${statsData.data.averageRoomSize}m²`);
      console.log(`   Average occupancy: ${statsData.data.averageOccupancy}%`);
    } else {
      console.log('❌ Failed to get statistics');
    }

    // Test 6: Search rooms
    console.log('\n6️⃣ Testing GET /api/rooms/search/query - Search functionality');
    const searchResponse = await fetch(`${SERVER_URL}/api/rooms/search/query?q=living`);
    const searchData = await searchResponse.json();
    
    if (searchData.success) {
      console.log(`✅ Search found ${searchData.count} rooms matching "living":`);
      searchData.data.forEach(room => {
        console.log(`   - ${room.name}`);
      });
    } else {
      console.log('❌ Search failed');
    }

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ Shared TypeScript classes work between client and server');
    console.log('✅ Room and FurnitureItem classes can be imported and used');
    console.log('✅ Validation functions work consistently');
    console.log('✅ JSON serialization/deserialization works');
    console.log('✅ Server can perform complex operations using shared logic');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\n💡 Make sure the server is running on http://localhost:3001');
    console.log('   Run: npm run dev:server');
  }
}

// Check if server is running first
async function checkServer() {
  try {
    const response = await fetch(`${SERVER_URL}/health`);
    if (response.ok) {
      return true;
    }
  } catch (error) {
    return false;
  }
  return false;
}

async function main() {
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    console.log('❌ Server is not running on http://localhost:3001');
    console.log('💡 Please start the server first:');
    console.log('   cd server && npm run dev');
    console.log('   or from root: npm run dev:server');
    process.exit(1);
  }

  await testSharedClasses();
}

main().catch(console.error);
