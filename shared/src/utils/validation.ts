/**
 * Validation utilities shared between client and server
 */

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * Validate a simple name field
 */
export function validateName(name: string): ValidationResult {
  const errors: string[] = [];

  if (!name || name.trim().length === 0) {
    errors.push('Name is required');
  }
  if (name && name.length > 50) {
    errors.push('Name cannot exceed 50 characters');
  }
  if (name && !/^[a-zA-Z0-9\s\-_]+$/.test(name)) {
    errors.push('Name can only contain letters, numbers, spaces, hyphens, and underscores');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate a numeric value
 */
export function validateValue(value: number, min: number = 0, max: number = 1000): ValidationResult {
  const errors: string[] = [];

  if (typeof value !== 'number' || isNaN(value)) {
    errors.push('Value must be a valid number');
  } else {
    if (value < min) {
      errors.push(`Value must be at least ${min}`);
    }
    if (value > max) {
      errors.push(`Value cannot exceed ${max}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate an array of items
 */
export function validateItems(items: string[]): ValidationResult {
  const errors: string[] = [];

  if (!Array.isArray(items)) {
    errors.push('Items must be an array');
  } else {
    if (items.length > 100) {
      errors.push('Cannot have more than 100 items');
    }

    items.forEach((item, index) => {
      if (typeof item !== 'string' || item.trim().length === 0) {
        errors.push(`Item at index ${index} must be a non-empty string`);
      }
      if (item && item.length > 100) {
        errors.push(`Item at index ${index} cannot exceed 100 characters`);
      }
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// Keep one legacy function for backward compatibility in existing code
export function validateRoomName(name: string): ValidationResult {
  return validateName(name);
}
