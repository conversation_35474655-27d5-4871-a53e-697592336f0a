/**
 * Validation utilities shared between client and server
 */

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * Validate room dimensions
 */
export function validateRoomDimensions(dimensions: {
  width: number;
  height: number;
  length: number;
}): ValidationResult {
  const errors: string[] = [];

  if (dimensions.width <= 0) {
    errors.push('Room width must be greater than 0');
  }
  if (dimensions.height <= 0) {
    errors.push('Room height must be greater than 0');
  }
  if (dimensions.length <= 0) {
    errors.push('Room length must be greater than 0');
  }

  // Reasonable limits for room dimensions (in meters)
  if (dimensions.width > 50) {
    errors.push('Room width cannot exceed 50 meters');
  }
  if (dimensions.height > 10) {
    errors.push('Room height cannot exceed 10 meters');
  }
  if (dimensions.length > 50) {
    errors.push('Room length cannot exceed 50 meters');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate furniture item data
 */
export function validateFurnitureItem(item: {
  name: string;
  category: string;
  dimensions: { width: number; height: number; length: number };
  position: { x: number; y: number; z: number };
}): ValidationResult {
  const errors: string[] = [];

  if (!item.name || item.name.trim().length === 0) {
    errors.push('Furniture name is required');
  }
  if (item.name && item.name.length > 100) {
    errors.push('Furniture name cannot exceed 100 characters');
  }

  if (!item.category || item.category.trim().length === 0) {
    errors.push('Furniture category is required');
  }

  // Validate dimensions
  if (item.dimensions.width <= 0) {
    errors.push('Furniture width must be greater than 0');
  }
  if (item.dimensions.height <= 0) {
    errors.push('Furniture height must be greater than 0');
  }
  if (item.dimensions.length <= 0) {
    errors.push('Furniture length must be greater than 0');
  }

  // Reasonable limits for furniture dimensions (in meters)
  if (item.dimensions.width > 10) {
    errors.push('Furniture width cannot exceed 10 meters');
  }
  if (item.dimensions.height > 5) {
    errors.push('Furniture height cannot exceed 5 meters');
  }
  if (item.dimensions.length > 10) {
    errors.push('Furniture length cannot exceed 10 meters');
  }

  // Validate position (should be non-negative)
  if (item.position.x < 0) {
    errors.push('Furniture X position cannot be negative');
  }
  if (item.position.y < 0) {
    errors.push('Furniture Y position cannot be negative');
  }
  if (item.position.z < 0) {
    errors.push('Furniture Z position cannot be negative');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate room name
 */
export function validateRoomName(name: string): ValidationResult {
  const errors: string[] = [];

  if (!name || name.trim().length === 0) {
    errors.push('Room name is required');
  }
  if (name && name.length > 50) {
    errors.push('Room name cannot exceed 50 characters');
  }
  if (name && !/^[a-zA-Z0-9\s\-_]+$/.test(name)) {
    errors.push('Room name can only contain letters, numbers, spaces, hyphens, and underscores');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
