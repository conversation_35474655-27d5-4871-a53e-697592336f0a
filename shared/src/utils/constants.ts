/**
 * Shared constants between client and server
 */

export const FURNITURE_CATEGORIES = {
  SEATING: 'seating',
  TABLES: 'tables',
  STORAGE: 'storage',
  BEDS: 'beds',
  LIGHTING: 'lighting',
  DECOR: 'decor',
  ELECTRONICS: 'electronics',
  OTHER: 'other'
} as const;

export const ROOM_TYPES = {
  LIVING_ROOM: 'living_room',
  BEDROOM: 'bedroom',
  KITCHEN: 'kitchen',
  BATHROOM: 'bathroom',
  DINING_ROOM: 'dining_room',
  OFFICE: 'office',
  GARAGE: 'garage',
  BASEMENT: 'basement',
  ATTIC: 'attic',
  OTHER: 'other'
} as const;

export const MATERIALS = {
  WOOD: 'wood',
  METAL: 'metal',
  PLASTIC: 'plastic',
  GLASS: 'glass',
  FABRIC: 'fabric',
  LEATHER: 'leather',
  STONE: 'stone',
  CERAMIC: 'ceramic',
  OTHER: 'other'
} as const;

export const DEFAULT_COLORS = {
  WHITE: '#FFFFFF',
  BLACK: '#000000',
  BROWN: '#8B4513',
  GRAY: '#808080',
  BEIGE: '#F5F5DC',
  BLUE: '#0000FF',
  GREEN: '#008000',
  RED: '#FF0000',
  YELLOW: '#FFFF00',
  PURPLE: '#800080'
} as const;

// API endpoints
export const API_ENDPOINTS = {
  ROOMS: '/api/rooms',
  FURNITURE: '/api/furniture',
  UPLOAD: '/api/upload',
  GENERATE: '/api/generate'
} as const;

// Validation limits
export const LIMITS = {
  ROOM: {
    MAX_WIDTH: 50,
    MAX_HEIGHT: 10,
    MAX_LENGTH: 50,
    MIN_DIMENSION: 0.1,
    MAX_NAME_LENGTH: 50
  },
  FURNITURE: {
    MAX_WIDTH: 10,
    MAX_HEIGHT: 5,
    MAX_LENGTH: 10,
    MIN_DIMENSION: 0.01,
    MAX_NAME_LENGTH: 100
  }
} as const;

// Type exports for the constants
export type FurnitureCategory = typeof FURNITURE_CATEGORIES[keyof typeof FURNITURE_CATEGORIES];
export type RoomType = typeof ROOM_TYPES[keyof typeof ROOM_TYPES];
export type Material = typeof MATERIALS[keyof typeof MATERIALS];
export type DefaultColor = typeof DEFAULT_COLORS[keyof typeof DEFAULT_COLORS];
