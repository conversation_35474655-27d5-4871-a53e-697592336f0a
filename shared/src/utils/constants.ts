/**
 * Shared constants between client and server
 */

export const OBJECT_CATEGORIES = {
  CATEGORY_A: 'category_a',
  CATEGORY_B: 'category_b',
  CATEGORY_C: 'category_c',
  OTHER: 'other'
} as const;

export const STATUS_TYPES = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  PENDING: 'pending',
  COMPLETED: 'completed'
} as const;

export const DEFAULT_COLORS = {
  WHITE: '#FFFFFF',
  BLACK: '#000000',
  BLUE: '#0000FF',
  GREEN: '#008000',
  RED: '#FF0000',
  GRAY: '#808080'
} as const;

// API endpoints
export const API_ENDPOINTS = {
  SHARED_OBJECTS: '/api/shared-objects',
  UPLOAD: '/api/upload',
  GENERATE: '/api/generate'
} as const;

// Validation limits
export const LIMITS = {
  OBJECT: {
    MAX_NAME_LENGTH: 50,
    MAX_VALUE: 1000,
    MIN_VALUE: 0,
    MAX_ITEMS: 100
  },
  ITEM: {
    MAX_LENGTH: 100,
    MIN_LENGTH: 1
  }
} as const;

// Type exports for the constants
export type ObjectCategory = typeof OBJECT_CATEGORIES[keyof typeof OBJECT_CATEGORIES];
export type StatusType = typeof STATUS_TYPES[keyof typeof STATUS_TYPES];
export type DefaultColor = typeof DEFAULT_COLORS[keyof typeof DEFAULT_COLORS];
