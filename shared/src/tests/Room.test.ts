import { Room, FurnitureItem, FURNITURE_CATEGORIES, validateRoomDimensions, validateFurnitureItem } from '../index.js';

/**
 * Simple test runner for the shared Room class
 * This demonstrates that the shared classes work correctly
 */

// Test utilities
function assert(condition: boolean, message: string): void {
  if (!condition) {
    throw new Error(`Assertion failed: ${message}`);
  }
}

function assertEqual<T>(actual: T, expected: T, message: string): void {
  if (actual !== expected) {
    throw new Error(`Assertion failed: ${message}. Expected: ${expected}, Actual: ${actual}`);
  }
}

function runTest(testName: string, testFn: () => void): void {
  try {
    testFn();
    console.log(`✅ ${testName}`);
  } catch (error) {
    console.error(`❌ ${testName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Test Room class
function testRoomCreation(): void {
  const room = new Room('test-id', 'Test Room', { width: 5, height: 3, length: 4 });
  
  assertEqual(room.id, 'test-id', 'Room ID should match');
  assertEqual(room.name, 'Test Room', 'Room name should match');
  assertEqual(room.dimensions.width, 5, 'Room width should match');
  assertEqual(room.dimensions.height, 3, 'Room height should match');
  assertEqual(room.dimensions.length, 4, 'Room length should match');
  assertEqual(room.furnitureItems.length, 0, 'Room should start with no furniture');
}

function testRoomCalculations(): void {
  const room = new Room('test-id', 'Test Room', { width: 5, height: 3, length: 4 });
  
  assertEqual(room.getFloorArea(), 20, 'Floor area should be width * length');
  assertEqual(room.getVolume(), 60, 'Volume should be width * height * length');
  assertEqual(room.getOccupiedFloorSpace(), 0, 'Occupied space should be 0 with no furniture');
  assertEqual(room.getAvailableSpacePercentage(), 100, 'Available space should be 100% with no furniture');
}

function testFurnitureOperations(): void {
  const room = new Room('test-id', 'Test Room', { width: 5, height: 3, length: 4 });
  
  const furniture = new FurnitureItem(
    'furniture-id',
    'Test Sofa',
    FURNITURE_CATEGORIES.SEATING,
    { width: 2, height: 0.8, length: 1 },
    { x: 1, y: 0, z: 1 }
  );
  
  // Test adding furniture
  room.addFurniture(furniture);
  assertEqual(room.furnitureItems.length, 1, 'Room should have 1 furniture item');
  assertEqual(room.getOccupiedFloorSpace(), 2, 'Occupied space should be 2m²');
  assertEqual(room.getAvailableSpacePercentage(), 90, 'Available space should be 90%');
  
  // Test removing furniture
  const removed = room.removeFurniture('furniture-id');
  assert(removed, 'Furniture should be removed successfully');
  assertEqual(room.furnitureItems.length, 0, 'Room should have no furniture after removal');
  assertEqual(room.getOccupiedFloorSpace(), 0, 'Occupied space should be 0 after removal');
  
  // Test removing non-existent furniture
  const notRemoved = room.removeFurniture('non-existent');
  assert(!notRemoved, 'Removing non-existent furniture should return false');
}

function testFurnitureItem(): void {
  const furniture = new FurnitureItem(
    'furniture-id',
    'Test Table',
    FURNITURE_CATEGORIES.TABLES,
    { width: 1.5, height: 0.8, length: 1 },
    { x: 2, y: 0, z: 3 },
    '#FFFFFF',
    'wood'
  );
  
  assertEqual(furniture.id, 'furniture-id', 'Furniture ID should match');
  assertEqual(furniture.name, 'Test Table', 'Furniture name should match');
  assertEqual(furniture.category, FURNITURE_CATEGORIES.TABLES, 'Furniture category should match');
  assertEqual(furniture.getFootprintArea(), 1.5, 'Footprint area should be width * length');
  
  // Test moving furniture
  furniture.moveTo(5, 1, 2);
  assertEqual(furniture.position.x, 5, 'X position should be updated');
  assertEqual(furniture.position.y, 1, 'Y position should be updated');
  assertEqual(furniture.position.z, 2, 'Z position should be updated');
}

function testJSONSerialization(): void {
  const room = new Room('test-id', 'Test Room', { width: 5, height: 3, length: 4 });
  
  const furniture = new FurnitureItem(
    'furniture-id',
    'Test Chair',
    FURNITURE_CATEGORIES.SEATING,
    { width: 0.6, height: 1, length: 0.6 },
    { x: 1, y: 0, z: 1 }
  );
  
  room.addFurniture(furniture);
  
  // Test room JSON serialization
  const roomJSON = room.toJSON();
  assertEqual(roomJSON.id, 'test-id', 'JSON ID should match');
  assertEqual(roomJSON.name, 'Test Room', 'JSON name should match');
  assertEqual(roomJSON.floorArea, 20, 'JSON floor area should be calculated');
  assertEqual(roomJSON.furnitureItems.length, 1, 'JSON should include furniture');
  
  // Test room deserialization
  const deserializedRoom = Room.fromJSON(roomJSON);
  assertEqual(deserializedRoom.id, room.id, 'Deserialized room ID should match');
  assertEqual(deserializedRoom.name, room.name, 'Deserialized room name should match');
  assertEqual(deserializedRoom.furnitureItems.length, 1, 'Deserialized room should have furniture');
  
  // Test furniture JSON serialization
  const furnitureJSON = furniture.toJSON();
  assertEqual(furnitureJSON.id, 'furniture-id', 'Furniture JSON ID should match');
  assertEqual(furnitureJSON.footprintArea, 0.36, 'Furniture JSON should include calculated footprint');
  
  // Test furniture deserialization
  const deserializedFurniture = FurnitureItem.fromJSON(furnitureJSON);
  assertEqual(deserializedFurniture.id, furniture.id, 'Deserialized furniture ID should match');
  assertEqual(deserializedFurniture.name, furniture.name, 'Deserialized furniture name should match');
}

function testValidation(): void {
  // Test valid room dimensions
  const validDimensions = { width: 5, height: 3, length: 4 };
  const validResult = validateRoomDimensions(validDimensions);
  assert(validResult.isValid, 'Valid dimensions should pass validation');
  assertEqual(validResult.errors.length, 0, 'Valid dimensions should have no errors');
  
  // Test invalid room dimensions
  const invalidDimensions = { width: -1, height: 0, length: 100 };
  const invalidResult = validateRoomDimensions(invalidDimensions);
  assert(!invalidResult.isValid, 'Invalid dimensions should fail validation');
  assert(invalidResult.errors.length > 0, 'Invalid dimensions should have errors');
  
  // Test valid furniture
  const validFurniture = {
    name: 'Test Furniture',
    category: FURNITURE_CATEGORIES.SEATING,
    dimensions: { width: 1, height: 1, length: 1 },
    position: { x: 0, y: 0, z: 0 }
  };
  const validFurnitureResult = validateFurnitureItem(validFurniture);
  assert(validFurnitureResult.isValid, 'Valid furniture should pass validation');
  
  // Test invalid furniture
  const invalidFurniture = {
    name: '',
    category: '',
    dimensions: { width: -1, height: 0, length: 20 },
    position: { x: -1, y: 0, z: 0 }
  };
  const invalidFurnitureResult = validateFurnitureItem(invalidFurniture);
  assert(!invalidFurnitureResult.isValid, 'Invalid furniture should fail validation');
  assert(invalidFurnitureResult.errors.length > 0, 'Invalid furniture should have errors');
}

function testFurnitureByCategory(): void {
  const room = new Room('test-id', 'Test Room', { width: 5, height: 3, length: 4 });
  
  const chair = new FurnitureItem('chair-id', 'Chair', FURNITURE_CATEGORIES.SEATING, { width: 0.6, height: 1, length: 0.6 });
  const table = new FurnitureItem('table-id', 'Table', FURNITURE_CATEGORIES.TABLES, { width: 1.2, height: 0.8, length: 0.8 });
  const sofa = new FurnitureItem('sofa-id', 'Sofa', FURNITURE_CATEGORIES.SEATING, { width: 2, height: 0.8, length: 1 });
  
  room.addFurniture(chair);
  room.addFurniture(table);
  room.addFurniture(sofa);
  
  const seatingItems = room.getFurnitureByCategory(FURNITURE_CATEGORIES.SEATING);
  assertEqual(seatingItems.length, 2, 'Should find 2 seating items');
  
  const tableItems = room.getFurnitureByCategory(FURNITURE_CATEGORIES.TABLES);
  assertEqual(tableItems.length, 1, 'Should find 1 table item');
  
  const storageItems = room.getFurnitureByCategory(FURNITURE_CATEGORIES.STORAGE);
  assertEqual(storageItems.length, 0, 'Should find 0 storage items');
}

// Run all tests
export function runAllTests(): void {
  console.log('🧪 Running shared Room class tests...\n');
  
  runTest('Room Creation', testRoomCreation);
  runTest('Room Calculations', testRoomCalculations);
  runTest('Furniture Operations', testFurnitureOperations);
  runTest('Furniture Item', testFurnitureItem);
  runTest('JSON Serialization', testJSONSerialization);
  runTest('Validation', testValidation);
  runTest('Furniture by Category', testFurnitureByCategory);
  
  console.log('\n✨ All tests completed!');
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests();
}
