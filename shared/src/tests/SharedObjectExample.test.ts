import { SharedObjectExample, validateRoomName } from '../index.js';

/**
 * Simple test runner for the shared SharedObjectExample class
 * This demonstrates that the shared classes work correctly
 */

// Test utilities
function assert(condition: boolean, message: string): void {
  if (!condition) {
    throw new Error(`Assertion failed: ${message}`);
  }
}

function assertEqual<T>(actual: T, expected: T, message: string): void {
  if (actual !== expected) {
    throw new Error(`Asser<PERSON> failed: ${message}. Expected: ${expected}, Actual: ${actual}`);
  }
}

function runTest(testName: string, testFn: () => void): void {
  try {
    testFn();
    console.log(`✅ ${testName}`);
  } catch (error) {
    console.error(`❌ ${testName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Test SharedObjectExample class
function testObjectCreation(): void {
  const obj = new SharedObjectExample('test-id', 'Test Object', 10);
  
  assertEqual(obj.id, 'test-id', 'Object ID should match');
  assertEqual(obj.name, 'Test Object', 'Object name should match');
  assertEqual(obj.value, 10, 'Object value should match');
  assertEqual(obj.items.length, 0, 'Object should start with no items');
}

function testObjectCalculations(): void {
  const obj = new SharedObjectExample('test-id', 'Test Object', 5);
  
  assertEqual(obj.getComputedValue(), 0, 'Computed value should be 0 with no items');
  assertEqual(obj.getSummary(), 'Test Object has 0 items with computed value: 0', 'Summary should be correct');
}

function testItemOperations(): void {
  const obj = new SharedObjectExample('test-id', 'Test Object', 3);
  
  // Test adding items
  obj.addItem('Item 1');
  obj.addItem('Item 2');
  assertEqual(obj.items.length, 2, 'Object should have 2 items');
  assertEqual(obj.getComputedValue(), 6, 'Computed value should be 3 * 2 = 6');
  
  // Test removing items
  const removed = obj.removeItem('Item 1');
  assert(removed, 'Item should be removed successfully');
  assertEqual(obj.items.length, 1, 'Object should have 1 item after removal');
  assertEqual(obj.getComputedValue(), 3, 'Computed value should be 3 * 1 = 3');
  
  // Test removing non-existent item
  const notRemoved = obj.removeItem('Non-existent');
  assert(!notRemoved, 'Removing non-existent item should return false');
}

function testJSONSerialization(): void {
  const obj = new SharedObjectExample('test-id', 'Test Object', 4);
  obj.addItem('Apple');
  obj.addItem('Banana');
  
  // Test JSON serialization
  const objJSON = obj.toJSON();
  assertEqual(objJSON.id, 'test-id', 'JSON ID should match');
  assertEqual(objJSON.name, 'Test Object', 'JSON name should match');
  assertEqual(objJSON.value, 4, 'JSON value should match');
  assertEqual(objJSON.items.length, 2, 'JSON should include items');
  assertEqual(objJSON.computedValue, 8, 'JSON should include computed value');
  assert(objJSON.summary.includes('Test Object'), 'JSON should include summary');
  
  // Test deserialization
  const deserializedObj = SharedObjectExample.fromJSON(objJSON);
  assertEqual(deserializedObj.id, obj.id, 'Deserialized object ID should match');
  assertEqual(deserializedObj.name, obj.name, 'Deserialized object name should match');
  assertEqual(deserializedObj.value, obj.value, 'Deserialized object value should match');
  assertEqual(deserializedObj.items.length, 2, 'Deserialized object should have items');
  assertEqual(deserializedObj.getComputedValue(), 8, 'Deserialized object computed value should match');
}

function testValidation(): void {
  // Test valid name
  const validResult = validateRoomName('Valid Name');
  assert(validResult.isValid, 'Valid name should pass validation');
  assertEqual(validResult.errors.length, 0, 'Valid name should have no errors');
  
  // Test invalid name
  const invalidResult = validateRoomName('');
  assert(!invalidResult.isValid, 'Empty name should fail validation');
  assert(invalidResult.errors.length > 0, 'Invalid name should have errors');
}

function testSummaryGeneration(): void {
  const obj = new SharedObjectExample('test-id', 'Demo Object', 2);
  obj.addItem('First');
  obj.addItem('Second');
  obj.addItem('Third');
  
  const summary = obj.getSummary();
  const expectedSummary = 'Demo Object has 3 items with computed value: 6';
  assertEqual(summary, expectedSummary, 'Summary should be correctly formatted');
}

// Run all tests
export function runAllTests(): void {
  console.log('🧪 Running shared SharedObjectExample class tests...\n');
  
  runTest('Object Creation', testObjectCreation);
  runTest('Object Calculations', testObjectCalculations);
  runTest('Item Operations', testItemOperations);
  runTest('JSON Serialization', testJSONSerialization);
  runTest('Validation', testValidation);
  runTest('Summary Generation', testSummaryGeneration);
  
  console.log('\n✨ All tests completed!');
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests();
}
