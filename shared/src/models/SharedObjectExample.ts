/**
 * Simple shared object example that can be used by both client and server
 */
export class SharedObjectExample {
  public id: string;
  public name: string;
  public value: number;
  public items: string[];
  public createdAt: Date;
  public updatedAt: Date;

  constructor(id: string, name: string, value: number = 0) {
    this.id = id;
    this.name = name;
    this.value = value;
    this.items = [];
    this.createdAt = new Date();
    this.updatedAt = new Date();
  }

  /**
   * Add an item to the collection
   */
  public addItem(item: string): void {
    this.items.push(item);
    this.updatedAt = new Date();
  }

  /**
   * Remove an item from the collection
   */
  public removeItem(item: string): boolean {
    const index = this.items.indexOf(item);
    if (index > -1) {
      this.items.splice(index, 1);
      this.updatedAt = new Date();
      return true;
    }
    return false;
  }

  /**
   * Calculate a simple computed value
   */
  public getComputedValue(): number {
    return this.value * this.items.length;
  }

  /**
   * Get summary information
   */
  public getSummary(): string {
    return `${this.name} has ${this.items.length} items with computed value: ${this.getComputedValue()}`;
  }

  /**
   * Convert object to JSON for API transmission
   */
  public toJSON(): SharedObjectJSON {
    return {
      id: this.id,
      name: this.name,
      value: this.value,
      items: this.items,
      createdAt: this.createdAt.toISOString(),
      updatedAt: this.updatedAt.toISOString(),
      computedValue: this.getComputedValue(),
      summary: this.getSummary()
    };
  }

  /**
   * Create SharedObjectExample instance from JSON data
   */
  public static fromJSON(data: SharedObjectJSON): SharedObjectExample {
    const obj = new SharedObjectExample(data.id, data.name, data.value);
    obj.items = data.items;
    obj.createdAt = new Date(data.createdAt);
    obj.updatedAt = new Date(data.updatedAt);
    return obj;
  }
}

// Type definition for JSON serialization
export interface SharedObjectJSON {
  id: string;
  name: string;
  value: number;
  items: string[];
  createdAt: string;
  updatedAt: string;
  computedValue: number;
  summary: string;
}
