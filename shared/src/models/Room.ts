/**
 * Represents a room in the home space modification app
 * This class is shared between client and server
 */
export class Room {
  public id: string;
  public name: string;
  public dimensions: {
    width: number;
    height: number;
    length: number;
  };
  public furnitureItems: FurnitureItem[];
  public createdAt: Date;
  public updatedAt: Date;

  constructor(
    id: string,
    name: string,
    dimensions: { width: number; height: number; length: number }
  ) {
    this.id = id;
    this.name = name;
    this.dimensions = dimensions;
    this.furnitureItems = [];
    this.createdAt = new Date();
    this.updatedAt = new Date();
  }

  /**
   * Calculate the total floor area of the room
   */
  public getFloorArea(): number {
    return this.dimensions.width * this.dimensions.length;
  }

  /**
   * Calculate the total volume of the room
   */
  public getVolume(): number {
    return this.dimensions.width * this.dimensions.height * this.dimensions.length;
  }

  /**
   * Add a furniture item to the room
   */
  public addFurniture(item: FurnitureItem): void {
    this.furnitureItems.push(item);
    this.updatedAt = new Date();
  }

  /**
   * Remove a furniture item from the room
   */
  public removeFurniture(itemId: string): boolean {
    const initialLength = this.furnitureItems.length;
    this.furnitureItems = this.furnitureItems.filter(item => item.id !== itemId);
    
    if (this.furnitureItems.length < initialLength) {
      this.updatedAt = new Date();
      return true;
    }
    return false;
  }

  /**
   * Get furniture items by category
   */
  public getFurnitureByCategory(category: string): FurnitureItem[] {
    return this.furnitureItems.filter(item => item.category === category);
  }

  /**
   * Calculate total occupied floor space by furniture
   */
  public getOccupiedFloorSpace(): number {
    return this.furnitureItems.reduce((total, item) => {
      return total + (item.dimensions.width * item.dimensions.length);
    }, 0);
  }

  /**
   * Get available floor space percentage
   */
  public getAvailableSpacePercentage(): number {
    const totalArea = this.getFloorArea();
    const occupiedArea = this.getOccupiedFloorSpace();
    return ((totalArea - occupiedArea) / totalArea) * 100;
  }

  /**
   * Convert room to JSON for API transmission
   */
  public toJSON(): RoomJSON {
    return {
      id: this.id,
      name: this.name,
      dimensions: this.dimensions,
      furnitureItems: this.furnitureItems.map(item => item.toJSON()),
      createdAt: this.createdAt.toISOString(),
      updatedAt: this.updatedAt.toISOString(),
      floorArea: this.getFloorArea(),
      volume: this.getVolume(),
      occupiedFloorSpace: this.getOccupiedFloorSpace(),
      availableSpacePercentage: this.getAvailableSpacePercentage()
    };
  }

  /**
   * Create Room instance from JSON data
   */
  public static fromJSON(data: RoomJSON): Room {
    const room = new Room(data.id, data.name, data.dimensions);
    room.createdAt = new Date(data.createdAt);
    room.updatedAt = new Date(data.updatedAt);
    room.furnitureItems = data.furnitureItems.map(item => FurnitureItem.fromJSON(item));
    return room;
  }
}

/**
 * Represents a furniture item in a room
 */
export class FurnitureItem {
  public id: string;
  public name: string;
  public category: string;
  public dimensions: {
    width: number;
    height: number;
    length: number;
  };
  public position: {
    x: number;
    y: number;
    z: number;
  };
  public color: string;
  public material: string;

  constructor(
    id: string,
    name: string,
    category: string,
    dimensions: { width: number; height: number; length: number },
    position: { x: number; y: number; z: number } = { x: 0, y: 0, z: 0 },
    color: string = '#FFFFFF',
    material: string = 'wood'
  ) {
    this.id = id;
    this.name = name;
    this.category = category;
    this.dimensions = dimensions;
    this.position = position;
    this.color = color;
    this.material = material;
  }

  /**
   * Move furniture to a new position
   */
  public moveTo(x: number, y: number, z: number): void {
    this.position = { x, y, z };
  }

  /**
   * Get the footprint area of the furniture
   */
  public getFootprintArea(): number {
    return this.dimensions.width * this.dimensions.length;
  }

  /**
   * Convert furniture item to JSON
   */
  public toJSON(): FurnitureItemJSON {
    return {
      id: this.id,
      name: this.name,
      category: this.category,
      dimensions: this.dimensions,
      position: this.position,
      color: this.color,
      material: this.material,
      footprintArea: this.getFootprintArea()
    };
  }

  /**
   * Create FurnitureItem instance from JSON data
   */
  public static fromJSON(data: FurnitureItemJSON): FurnitureItem {
    return new FurnitureItem(
      data.id,
      data.name,
      data.category,
      data.dimensions,
      data.position,
      data.color,
      data.material
    );
  }
}

// Type definitions for JSON serialization
export interface RoomJSON {
  id: string;
  name: string;
  dimensions: { width: number; height: number; length: number };
  furnitureItems: FurnitureItemJSON[];
  createdAt: string;
  updatedAt: string;
  floorArea: number;
  volume: number;
  occupiedFloorSpace: number;
  availableSpacePercentage: number;
}

export interface FurnitureItemJSON {
  id: string;
  name: string;
  category: string;
  dimensions: { width: number; height: number; length: number };
  position: { x: number; y: number; z: number };
  color: string;
  material: string;
  footprintArea: number;
}
