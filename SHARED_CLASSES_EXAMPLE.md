# Shared TypeScript Classes Example

This document demonstrates how TypeScript classes can be shared between client and server in the current monorepo setup.

## 🏗️ Architecture Overview

The project now has a **shared package** that contains TypeScript classes and utilities that can be used by both the client and server:

```
vibemyspace/
├── shared/           # 🆕 Shared TypeScript package
│   ├── src/
│   │   ├── models/
│   │   │   └── Room.ts      # Room and FurnitureItem classes
│   │   ├── utils/
│   │   │   ├── validation.ts # Shared validation functions
│   │   │   └── constants.ts  # Shared constants
│   │   └── index.ts         # Main exports
│   ├── package.json
│   └── tsconfig.json
├── client/           # React frontend
├── server/           # Express backend
└── package.json      # Root workspace config
```

## 🔧 Setup Details

### 1. Shared Package Configuration

The shared package (`shared/package.json`) is configured as an ES module with TypeScript compilation:

```json
{
  "name": "vibe-my-home-shared",
  "type": "module",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "scripts": {
    "build": "tsc",
    "dev": "tsc --watch"
  }
}
```

### 2. Workspace Dependencies

Both client and server reference the shared package:

```json
// client/package.json & server/package.json
{
  "dependencies": {
    "vibe-my-home-shared": "file:../shared"
  }
}
```

### 3. Root Workspace Configuration

The root `package.json` includes all three packages:

```json
{
  "workspaces": ["client", "server", "shared"],
  "scripts": {
    "build": "npm run build:shared && npm run build:client && npm run build:server",
    "dev": "concurrently \"npm run dev:shared\" \"npm run dev:client\" \"npm run dev:server\""
  }
}
```

## 📝 Example Classes

### Room Class

The `Room` class demonstrates complex shared logic:

```typescript
export class Room {
  public id: string;
  public name: string;
  public dimensions: { width: number; height: number; length: number };
  public furnitureItems: FurnitureItem[];

  // Calculated properties
  public getFloorArea(): number {
    return this.dimensions.width * this.dimensions.length;
  }

  public getAvailableSpacePercentage(): number {
    const totalArea = this.getFloorArea();
    const occupiedArea = this.getOccupiedFloorSpace();
    return ((totalArea - occupiedArea) / totalArea) * 100;
  }

  // JSON serialization for API communication
  public toJSON(): RoomJSON { /* ... */ }
  public static fromJSON(data: RoomJSON): Room { /* ... */ }
}
```

### Shared Validation

Validation functions work identically on both client and server:

```typescript
export function validateRoomDimensions(dimensions: {
  width: number; height: number; length: number;
}): ValidationResult {
  const errors: string[] = [];
  
  if (dimensions.width <= 0) {
    errors.push('Room width must be greater than 0');
  }
  // ... more validation
  
  return { isValid: errors.length === 0, errors };
}
```

## 🖥️ Client Usage

The React component (`client/src/components/RoomManager.tsx`) uses shared classes:

```typescript
import { Room, FurnitureItem, FURNITURE_CATEGORIES, validateRoomName } from 'vibe-my-home-shared';

export const RoomManager: React.FC = () => {
  const [rooms, setRooms] = useState<Room[]>([]);

  const createRoom = () => {
    const validation = validateRoomName(newRoomName);
    if (!validation.isValid) {
      alert(`Invalid room name: ${validation.errors.join(', ')}`);
      return;
    }

    const newRoom = new Room(uuidv4(), newRoomName, dimensions);
    setRooms([...rooms, newRoom]);
  };
  
  // ... component renders room data using shared methods
};
```

## 🖧 Server Usage

The server service (`server/src/roomService.ts`) uses the same classes:

```typescript
import { Room, FurnitureItem, validateRoomDimensions } from 'vibe-my-home-shared';

export class RoomService {
  private rooms: Map<string, Room> = new Map();

  public createRoom(name: string, dimensions: any) {
    const validation = validateRoomDimensions(dimensions);
    if (!validation.isValid) {
      return { success: false, errors: validation.errors };
    }

    const newRoom = new Room(uuidv4(), name, dimensions);
    this.rooms.set(newRoom.id, newRoom);
    
    return { success: true, room: newRoom.toJSON() };
  }
}
```

## 🧪 Testing Results

The test script (`test-shared-classes.js`) successfully demonstrated:

✅ **Shared classes work between client and server**
- Room and FurnitureItem classes imported and used correctly
- Complex calculations (floor area, occupancy) work identically

✅ **Validation functions work consistently**
- Same validation logic on both client and server
- Prevents invalid data from being processed

✅ **JSON serialization/deserialization works**
- Objects can be safely transmitted via API
- Calculated properties included in JSON output

✅ **Server performs complex operations using shared logic**
- Room statistics calculated using shared methods
- Search functionality works with shared data structures

## 🚀 Build and Run

### Build Everything
```bash
npm run build
```

### Run in Development
```bash
npm run dev
```

### Test the Shared Classes
```bash
node test-shared-classes.js
```

## 📊 Test Results Summary

When running the test script with the server active:

```
🧪 Testing shared TypeScript classes between client and server...

1️⃣ Testing GET /api/rooms - Fetch all rooms
✅ Found 3 rooms with calculated properties

2️⃣ Testing POST /api/rooms - Create new room  
✅ Created room with shared validation

3️⃣ Testing POST /api/rooms/:id/furniture - Add furniture
✅ Added furniture with occupancy calculations

4️⃣ Testing validation - Try to create invalid room
✅ Validation correctly rejected invalid data

5️⃣ Testing GET /api/rooms/stats/summary - Room statistics
✅ Statistics calculated using shared methods

6️⃣ Testing GET /api/rooms/search/query - Search functionality
✅ Search works with shared data structures

🎉 All tests completed successfully!
```

## 🎯 Key Benefits

1. **Code Reuse**: Business logic written once, used everywhere
2. **Type Safety**: Full TypeScript support across client and server
3. **Consistency**: Same validation and calculations on both sides
4. **Maintainability**: Changes to shared logic automatically apply everywhere
5. **API Safety**: Structured data transfer with type checking

This setup provides a robust foundation for sharing complex TypeScript classes between client and server in a monorepo architecture.
